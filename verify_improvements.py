#!/usr/bin/env python3
"""
验证绘图器改进效果
================

快速验证全屏显示和日期hover标签的改进效果
"""

from lB_BT_Plotly import BacktestSystem
from datetime import datetime


def main():
    """
    主验证函数
    """
    print("=" * 50)
    print("验证绘图器改进效果")
    print("=" * 50)
    
    # 创建回测系统（使用默认的全屏绘图器）
    backtest_system = BacktestSystem()
    
    # 设置回测参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 10, 1)
    end_date = datetime(2023, 12, 31)
    initial_cash = 100000
    
    print(f"股票代码: {symbol}")
    print(f"时间范围: {start_date.date()} 到 {end_date.date()}")
    print(f"初始资金: ${initial_cash:,}")
    
    print("\n改进验证项目:")
    print("1. ✅ 全屏显示 - 图表将占据整个浏览器窗口")
    print("2. ✅ 日期hover标签 - 鼠标悬停显示具体日期而非编号")
    print("3. ✅ 详细hover信息 - 显示完整的价格和指标信息")
    
    # 运行回测
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash
    )
    
    if results:
        print(f"\n生成改进后的图表...")
        fig = backtest_system.plot_results(symbol)
        
        if fig:
            # 验证图表配置
            print(f"\n图表配置验证:")
            print(f"- 自动调整大小: {fig.layout.autosize}")
            print(f"- 宽度设置: {fig.layout.width}")
            print(f"- 高度设置: {fig.layout.height}")
            
            # 验证hover配置
            has_custom_hover = False
            for trace in fig.data:
                if hasattr(trace, 'hovertext') and trace.hovertext:
                    has_custom_hover = True
                    break
            
            print(f"- 自定义hover信息: {'已启用' if has_custom_hover else '未启用'}")
            
            # 显示图表
            fig.show()
            
            print(f"\n🎉 改进验证完成！")
            print("\n请在浏览器中验证以下改进:")
            print("=" * 50)
            print("1. 全屏显示验证:")
            print("   - 图表是否占据了整个浏览器窗口？")
            print("   - 图表是否自动适应窗口大小？")
            
            print("\n2. 日期hover标签验证:")
            print("   - 将鼠标悬停在K线上")
            print("   - 是否显示'日期: 2023-XX-XX'而不是数字编号？")
            print("   - 是否显示完整的OHLCV信息？")
            
            print("\n3. 交易信号hover验证:")
            print("   - 将鼠标悬停在绿色买入信号点上")
            print("   - 是否显示'买入信号'和具体日期？")
            print("   - 将鼠标悬停在红色卖出信号点上")
            print("   - 是否显示'卖出信号'和具体日期？")
            
            print("\n4. MACD指标hover验证:")
            print("   - 将鼠标悬停在MACD线上")
            print("   - 是否显示日期和MACD数值？")
            print("   - 将鼠标悬停在直方图上")
            print("   - 是否显示日期和直方图数值？")
            
            print("\n" + "=" * 50)
            print("如果以上验证都通过，说明改进成功！")
            print("=" * 50)
            
        else:
            print("❌ 图表生成失败")
    else:
        print("❌ 回测失败")


if __name__ == "__main__":
    main()
