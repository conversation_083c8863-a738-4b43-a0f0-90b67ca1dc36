"""
Backtrader 策略参数传递示例
==========================

演示如何在 BacktestSystem 和 MACDStrategy 之间正确传递参数
"""

import backtrader as bt
import pandas as pd
from datetime import datetime


class MACDStrategy(bt.Strategy):
    """
    MACD策略 - 支持参数传递
    """
    
    # 策略参数定义 - 这是关键部分
    params = (
        ('fast_period', 12),     # MACD快线周期
        ('slow_period', 26),     # MACD慢线周期  
        ('signal_period', 9),    # MACD信号线周期
        ('printlog', True),      # 是否打印交易日志
        ('stop_loss', 0.05),     # 止损比例（新增参数）
        ('take_profit', 0.10),   # 止盈比例（新增参数）
        ('position_size', 0.95), # 仓位大小比例（新增参数）
    )
    
    def __init__(self):
        """
        策略初始化 - 在这里可以访问所有传入的参数
        """
        print(f"策略初始化，参数设置：")
        print(f"  MACD快线周期: {self.params.fast_period}")
        print(f"  MACD慢线周期: {self.params.slow_period}")
        print(f"  MACD信号线周期: {self.params.signal_period}")
        print(f"  止损比例: {self.params.stop_loss}")
        print(f"  止盈比例: {self.params.take_profit}")
        print(f"  仓位大小: {self.params.position_size}")
        
        # 使用传入的参数计算MACD指标
        self.macd = bt.indicators.MACDHisto(
            self.data.close,
            period_me1=self.params.fast_period,    # 使用传入的快线周期
            period_me2=self.params.slow_period,    # 使用传入的慢线周期
            period_signal=self.params.signal_period # 使用传入的信号线周期
        )
        
        self.macd_line = self.macd.macd
        self.signal_line = self.macd.signal
        self.histogram = self.macd.histo
        
        # 创建交叉信号
        self.crossover = bt.indicators.CrossOver(self.macd_line, self.signal_line)
        
        # 初始化交易状态
        self.order = None
        self.buy_price = None
        
    def next(self):
        """
        策略主逻辑 - 使用传入的参数进行交易决策
        """
        if self.order:
            return
            
        # 买入信号
        if not self.position and self.crossover > 0:
            # 使用传入的仓位大小参数
            size = int(self.broker.getcash() * self.params.position_size / self.data.close[0])
            self.order = self.buy(size=size)
            self.buy_price = self.data.close[0]
            if self.params.printlog:
                self.log(f'买入信号，价格: {self.data.close[0]:.2f}, 数量: {size}')
                
        # 卖出信号或止损/止盈
        elif self.position:
            current_price = self.data.close[0]
            
            # 计算收益率
            if self.buy_price:
                return_rate = (current_price - self.buy_price) / self.buy_price
                
                # 止损条件
                if return_rate <= -self.params.stop_loss:
                    self.order = self.sell()
                    if self.params.printlog:
                        self.log(f'止损卖出，价格: {current_price:.2f}, 亏损: {return_rate:.2%}')
                        
                # 止盈条件
                elif return_rate >= self.params.take_profit:
                    self.order = self.sell()
                    if self.params.printlog:
                        self.log(f'止盈卖出，价格: {current_price:.2f}, 盈利: {return_rate:.2%}')
                        
                # MACD死叉卖出
                elif self.crossover < 0:
                    self.order = self.sell()
                    if self.params.printlog:
                        self.log(f'MACD死叉卖出，价格: {current_price:.2f}, 收益: {return_rate:.2%}')
    
    def log(self, txt, dt=None):
        """日志输出"""
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}, {txt}')


class EnhancedBacktestSystem:
    """
    增强的回测系统 - 支持策略参数传递
    """
    
    def __init__(self):
        self.results = {}
    
    def run_backtest_with_params(self, 
                                symbol, 
                                data, 
                                initial_cash=100000,
                                commission=0.001,
                                # 策略参数
                                fast_period=12,
                                slow_period=26, 
                                signal_period=9,
                                stop_loss=0.05,
                                take_profit=0.10,
                                position_size=0.95,
                                printlog=True):
        """
        运行带参数的回测
        
        Args:
            symbol: 股票代码
            data: 价格数据DataFrame
            initial_cash: 初始资金
            commission: 手续费率
            fast_period: MACD快线周期
            slow_period: MACD慢线周期
            signal_period: MACD信号线周期
            stop_loss: 止损比例
            take_profit: 止盈比例
            position_size: 仓位大小比例
            printlog: 是否打印日志
        """
        
        print(f"\n{'='*50}")
        print(f"开始回测 {symbol}")
        print(f"策略参数:")
        print(f"  初始资金: ${initial_cash:,.2f}")
        print(f"  手续费率: {commission:.3f}")
        print(f"  MACD参数: ({fast_period}, {slow_period}, {signal_period})")
        print(f"  止损比例: {stop_loss:.1%}")
        print(f"  止盈比例: {take_profit:.1%}")
        print(f"  仓位大小: {position_size:.1%}")
        print(f"{'='*50}")
        
        # 创建Backtrader引擎
        cerebro = bt.Cerebro()
        
        # 添加数据
        bt_data = bt.feeds.PandasData(dataname=data)
        cerebro.adddata(bt_data)
        
        # 关键：添加策略并传递参数
        cerebro.addstrategy(
            MACDStrategy,
            fast_period=fast_period,      # 传递MACD快线周期
            slow_period=slow_period,      # 传递MACD慢线周期
            signal_period=signal_period,  # 传递MACD信号线周期
            stop_loss=stop_loss,          # 传递止损比例
            take_profit=take_profit,      # 传递止盈比例
            position_size=position_size,  # 传递仓位大小
            printlog=printlog             # 传递日志开关
        )
        
        # 设置初始资金和手续费
        cerebro.broker.setcash(initial_cash)
        cerebro.broker.setcommission(commission=commission)
        
        # 添加分析器
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")
        
        # 执行回测
        start_value = cerebro.broker.getvalue()
        results = cerebro.run()
        end_value = cerebro.broker.getvalue()
        
        # 提取结果
        strategy = results[0]
        total_return = ((end_value - start_value) / start_value) * 100
        
        # 整理结果
        results_dict = {
            'symbol': symbol,
            'initial_cash': initial_cash,
            'start_value': start_value,
            'end_value': end_value,
            'total_return': total_return,
            'strategy_params': {
                'fast_period': fast_period,
                'slow_period': slow_period,
                'signal_period': signal_period,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'position_size': position_size
            }
        }
        
        self.results[symbol] = results_dict
        self.print_results(results_dict)
        
        return results_dict
    
    def print_results(self, results):
        """打印回测结果"""
        print(f"\n{'='*30} 回测结果 {'='*30}")
        print(f"股票代码: {results['symbol']}")
        print(f"初始资金: ${results['initial_cash']:,.2f}")
        print(f"最终资金: ${results['end_value']:,.2f}")
        print(f"总收益率: {results['total_return']:.2f}%")
        
        print(f"\n策略参数:")
        params = results['strategy_params']
        print(f"  MACD参数: ({params['fast_period']}, {params['slow_period']}, {params['signal_period']})")
        print(f"  止损比例: {params['stop_loss']:.1%}")
        print(f"  止盈比例: {params['take_profit']:.1%}")
        print(f"  仓位大小: {params['position_size']:.1%}")
        print(f"{'='*70}")


def demo_parameter_passing():
    """
    演示参数传递功能
    """
    # 创建模拟数据
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    np.random.seed(42)
    prices = 100 + np.cumsum(np.random.randn(len(dates)) * 0.5)
    
    data = pd.DataFrame({
        'open': prices * (1 + np.random.randn(len(dates)) * 0.01),
        'high': prices * (1 + np.abs(np.random.randn(len(dates))) * 0.02),
        'low': prices * (1 - np.abs(np.random.randn(len(dates))) * 0.02),
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, len(dates))
    }, index=dates)
    
    # 创建回测系统
    system = EnhancedBacktestSystem()
    
    print("="*60)
    print("策略参数传递演示")
    print("="*60)
    
    # 演示1：使用默认参数
    print("\n1. 使用默认参数的回测")
    results1 = system.run_backtest_with_params(
        symbol="TEST.US",
        data=data,
        initial_cash=100000
    )
    
    # 演示2：自定义MACD参数
    print("\n2. 自定义MACD参数的回测")
    results2 = system.run_backtest_with_params(
        symbol="TEST.US",
        data=data,
        initial_cash=100000,
        fast_period=8,      # 更快的快线
        slow_period=21,     # 更快的慢线
        signal_period=5,    # 更快的信号线
        stop_loss=0.03,     # 更严格的止损
        take_profit=0.15,   # 更高的止盈目标
        position_size=0.8   # 更保守的仓位
    )
    
    # 演示3：激进参数设置
    print("\n3. 激进参数设置的回测")
    results3 = system.run_backtest_with_params(
        symbol="TEST.US", 
        data=data,
        initial_cash=100000,
        fast_period=5,      # 非常快的快线
        slow_period=13,     # 非常快的慢线
        signal_period=3,    # 非常快的信号线
        stop_loss=0.02,     # 非常严格的止损
        take_profit=0.08,   # 较低的止盈目标
        position_size=1.0,  # 满仓操作
        printlog=False      # 关闭详细日志
    )


if __name__ == "__main__":
    import numpy as np
    demo_parameter_passing()
