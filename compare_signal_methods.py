"""
交易信号方法对比测试
==================

比较两种交易信号方法的效果：
1. 直方图差分信号（原始方法）
2. 传统MACD交叉信号（更保守）

目的是找出哪种方法能产生更合理的胜率。
"""

import backtrader as bt
import pandas as pd
from datetime import datetime
from lB_BT_Plotly import LongBridgeData, MACDStrategy
from longport.openapi import Period

def run_backtest_with_signal_method(symbol, start_date, end_date, initial_cash, use_traditional_signal=False):
    """
    使用指定的信号方法运行回测
    """
    # 下载数据
    data_downloader = LongBridgeData(enable_cache=True)
    df = data_downloader.download_data(symbol, start_date, end_date, period=Period.Day)
    
    if df is None or len(df) == 0:
        print(f"无法获取 {symbol} 的数据")
        return None
    
    # 创建回测引擎
    cerebro = bt.Cerebro(runonce=True)
    
    # 添加数据
    data = bt.feeds.PandasData(dataname=df)
    cerebro.adddata(data)
    cerebro.origin_df = df
    
    # 添加策略（指定信号方法）
    cerebro.addstrategy(
        MACDStrategy, 
        origin_df=df,
        use_traditional_signal=use_traditional_signal,
        printlog=False  # 关闭详细日志以减少输出
    )
    
    # 设置初始资金和手续费
    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=0.001)
    
    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")
    
    # 运行回测
    start_value = cerebro.broker.getvalue()
    results = cerebro.run()
    end_value = cerebro.broker.getvalue()
    
    # 提取结果
    strategy = results[0]
    trade_analyzer = strategy.analyzers.trades.get_analysis()
    sharpe_ratio = strategy.analyzers.sharpe.get_analysis().get('sharperatio', 0)
    drawdown = strategy.analyzers.drawdown.get_analysis()
    
    # 计算统计指标
    total_return = ((end_value - start_value) / start_value) * 100
    trade_count = trade_analyzer.get('total', {}).get('total', 0)
    win_count = trade_analyzer.get('won', {}).get('total', 0)
    lose_count = trade_analyzer.get('lost', {}).get('total', 0)
    win_rate = (win_count / trade_count * 100) if trade_count > 0 else 0
    
    return {
        'signal_method': '传统MACD交叉' if use_traditional_signal else '直方图差分',
        'total_return': total_return,
        'trade_count': trade_count,
        'win_count': win_count,
        'lose_count': lose_count,
        'win_rate': win_rate,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),
        'start_value': start_value,
        'end_value': end_value
    }

def compare_signal_methods():
    """
    比较两种信号方法
    """
    print("="*80)
    print("交易信号方法对比测试")
    print("="*80)
    
    # 测试参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    initial_cash = 100000
    
    print(f"测试股票: {symbol}")
    print(f"测试时间: {start_date.date()} 到 {end_date.date()}")
    print(f"初始资金: ${initial_cash:,}")
    print("-"*80)
    
    # 测试两种方法
    print("\n1. 测试直方图差分信号方法（原始方法）...")
    result1 = run_backtest_with_signal_method(
        symbol, start_date, end_date, initial_cash, use_traditional_signal=False
    )
    
    print("\n2. 测试传统MACD交叉信号方法（保守方法）...")
    result2 = run_backtest_with_signal_method(
        symbol, start_date, end_date, initial_cash, use_traditional_signal=True
    )
    
    # 比较结果
    if result1 and result2:
        print("\n" + "="*80)
        print("对比结果")
        print("="*80)
        
        # 创建对比表格
        print(f"{'指标':<20} {'直方图差分':<15} {'传统MACD交叉':<15} {'差异':<15}")
        print("-" * 80)
        
        # 收益率对比
        return_diff = result2['total_return'] - result1['total_return']
        print(f"{'总收益率 (%)':<20} {result1['total_return']:<15.2f} {result2['total_return']:<15.2f} {return_diff:+.2f}")
        
        # 交易次数对比
        trade_diff = result2['trade_count'] - result1['trade_count']
        print(f"{'交易次数':<20} {result1['trade_count']:<15} {result2['trade_count']:<15} {trade_diff:+}")
        
        # 胜率对比
        winrate_diff = result2['win_rate'] - result1['win_rate']
        print(f"{'胜率 (%)':<20} {result1['win_rate']:<15.1f} {result2['win_rate']:<15.1f} {winrate_diff:+.1f}")
        
        # 盈利交易对比
        win_diff = result2['win_count'] - result1['win_count']
        print(f"{'盈利交易':<20} {result1['win_count']:<15} {result2['win_count']:<15} {win_diff:+}")
        
        # 亏损交易对比
        lose_diff = result2['lose_count'] - result1['lose_count']
        print(f"{'亏损交易':<20} {result1['lose_count']:<15} {result2['lose_count']:<15} {lose_diff:+}")
        
        # 最大回撤对比
        dd_diff = result2['max_drawdown'] - result1['max_drawdown']
        print(f"{'最大回撤 (%)':<20} {result1['max_drawdown']:<15.2f} {result2['max_drawdown']:<15.2f} {dd_diff:+.2f}")
        
        # 夏普比率对比
        if result1['sharpe_ratio'] and result2['sharpe_ratio']:
            sharpe_diff = result2['sharpe_ratio'] - result1['sharpe_ratio']
            print(f"{'夏普比率':<20} {result1['sharpe_ratio']:<15.4f} {result2['sharpe_ratio']:<15.4f} {sharpe_diff:+.4f}")
        
        print("\n" + "="*80)
        print("分析结论")
        print("="*80)
        
        # 胜率分析
        if result1['win_rate'] == 100.0:
            print("⚠️  直方图差分方法胜率100%，可能存在过拟合或信号过于频繁")
        elif result1['win_rate'] > 80.0:
            print("⚠️  直方图差分方法胜率过高，可能不太现实")
        else:
            print("✅ 直方图差分方法胜率看起来合理")
            
        if result2['win_rate'] == 100.0:
            print("⚠️  传统MACD方法胜率100%，可能存在过拟合")
        elif result2['win_rate'] > 80.0:
            print("⚠️  传统MACD方法胜率过高，可能不太现实")
        else:
            print("✅ 传统MACD方法胜率看起来合理")
        
        # 交易频率分析
        if result1['trade_count'] > result2['trade_count'] * 2:
            print("📊 直方图差分方法交易更频繁，可能产生更多噪音信号")
        elif result2['trade_count'] > result1['trade_count'] * 2:
            print("📊 传统MACD方法交易更频繁")
        else:
            print("📊 两种方法交易频率相近")
        
        # 推荐
        print(f"\n💡 推荐:")
        if result2['win_rate'] < result1['win_rate'] and result2['win_rate'] < 80:
            print("   建议使用传统MACD交叉信号，胜率更合理")
        elif result1['trade_count'] < result2['trade_count'] and result1['win_rate'] < 80:
            print("   建议使用直方图差分信号，但需要进一步优化")
        else:
            print("   两种方法各有优劣，建议根据具体需求选择")
    
    return result1, result2

def test_multiple_stocks():
    """
    在多个股票上测试两种方法
    """
    print("\n" + "="*80)
    print("多股票测试")
    print("="*80)
    
    symbols = ["AAPL.US", "MSFT.US", "GOOGL.US"]
    results = []
    
    for symbol in symbols:
        print(f"\n测试股票: {symbol}")
        
        # 测试两种方法
        result1 = run_backtest_with_signal_method(
            symbol, datetime(2023, 1, 1), datetime(2023, 6, 30), 100000, False
        )
        result2 = run_backtest_with_signal_method(
            symbol, datetime(2023, 1, 1), datetime(2023, 6, 30), 100000, True
        )
        
        if result1 and result2:
            results.append({
                'symbol': symbol,
                'histogram_winrate': result1['win_rate'],
                'traditional_winrate': result2['win_rate'],
                'histogram_trades': result1['trade_count'],
                'traditional_trades': result2['trade_count']
            })
    
    # 汇总结果
    if results:
        print(f"\n多股票胜率汇总:")
        print(f"{'股票':<10} {'直方图胜率':<12} {'传统胜率':<12} {'直方图交易':<12} {'传统交易':<12}")
        print("-" * 70)
        
        for result in results:
            print(f"{result['symbol']:<10} {result['histogram_winrate']:<12.1f} "
                  f"{result['traditional_winrate']:<12.1f} {result['histogram_trades']:<12} "
                  f"{result['traditional_trades']:<12}")

def main():
    """
    主函数
    """
    try:
        # 主要对比测试
        compare_signal_methods()
        
        # 多股票测试
        test_multiple_stocks()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
