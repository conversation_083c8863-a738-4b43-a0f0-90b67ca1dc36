"""
交易周期配置文件
===============

用于配置MACD直方图差分交易系统的交易周期和相关参数
"""

# 尝试导入longport，如果失败则使用模拟值
try:
    from longport.openapi import Period
    LONGPORT_AVAILABLE = True
except ImportError:
    # 模拟Period类用于演示
    class Period:
        Min_1 = "Min_1"
        Min_5 = "Min_5"
        Min_15 = "Min_15"
        Min_30 = "Min_30"
        Min_60 = "Min_60"
        Day = "Day"
    LONGPORT_AVAILABLE = False

class TradingPeriodConfig:
    """交易周期配置类"""
    
    # 支持的交易周期配置
    SUPPORTED_PERIODS = {
        '1m': {
            'period': Period.Min_1,
            'name': '1分钟',
            'seconds': 60,
            'description': '1分钟K线，适合超短线交易',
            'recommended_lookback': 200,  # 推荐回看周期数
            'min_data_points': 50,        # 最少数据点
        },
        '5m': {
            'period': Period.Min_5,
            'name': '5分钟',
            'seconds': 300,
            'description': '5分钟K线，适合短线交易',
            'recommended_lookback': 150,
            'min_data_points': 40,
        },
        '15m': {
            'period': Period.Min_15,
            'name': '15分钟',
            'seconds': 900,
            'description': '15分钟K线，适合日内交易',
            'recommended_lookback': 120,
            'min_data_points': 35,
        },
        '30m': {
            'period': Period.Min_30,
            'name': '30分钟',
            'seconds': 1800,
            'description': '30分钟K线，适合中短线交易',
            'recommended_lookback': 100,
            'min_data_points': 30,
        },
        '1h': {
            'period': Period.Min_60,
            'name': '1小时',
            'seconds': 3600,
            'description': '1小时K线，适合中线交易',
            'recommended_lookback': 100,
            'min_data_points': 26,
        },
        '1d': {
            'period': Period.Day,
            'name': '1天',
            'seconds': 86400,
            'description': '日K线，适合长线交易',
            'recommended_lookback': 60,
            'min_data_points': 26,
        },
    }
    
    @classmethod
    def get_period_info(cls, period_key):
        """
        获取周期信息
        
        Args:
            period_key (str): 周期键值
            
        Returns:
            dict: 周期信息
        """
        if period_key not in cls.SUPPORTED_PERIODS:
            raise ValueError(f"不支持的交易周期: {period_key}. 支持的周期: {list(cls.SUPPORTED_PERIODS.keys())}")
        
        return cls.SUPPORTED_PERIODS[period_key]
    
    @classmethod
    def get_all_periods(cls):
        """获取所有支持的周期"""
        return cls.SUPPORTED_PERIODS
    
    @classmethod
    def validate_period(cls, period_key):
        """验证周期是否支持"""
        return period_key in cls.SUPPORTED_PERIODS
    
    @classmethod
    def get_recommended_config(cls, period_key):
        """
        获取推荐的配置参数
        
        Args:
            period_key (str): 周期键值
            
        Returns:
            dict: 推荐配置
        """
        period_info = cls.get_period_info(period_key)
        
        return {
            'period': period_key,
            'update_interval': period_info['seconds'],
            'lookback_periods': period_info['recommended_lookback'],
            'min_data_points': period_info['min_data_points'],
            'macd_config': cls._get_macd_config_for_period(period_key),
            'filter_config': cls._get_filter_config_for_period(period_key),
        }
    
    @classmethod
    def _get_macd_config_for_period(cls, period_key):
        """根据周期获取MACD配置"""
        # 不同周期的MACD参数建议
        macd_configs = {
            '1m': {'fast': 12, 'slow': 26, 'signal': 9},   # 标准参数
            '5m': {'fast': 12, 'slow': 26, 'signal': 9},   # 标准参数
            '15m': {'fast': 12, 'slow': 26, 'signal': 9},  # 标准参数
            '30m': {'fast': 12, 'slow': 26, 'signal': 9},  # 标准参数
            '1h': {'fast': 12, 'slow': 26, 'signal': 9},   # 标准参数
            '1d': {'fast': 12, 'slow': 26, 'signal': 9},   # 标准参数
        }
        
        return macd_configs.get(period_key, {'fast': 12, 'slow': 26, 'signal': 9})
    
    @classmethod
    def _get_filter_config_for_period(cls, period_key):
        """根据周期获取滤波配置"""
        # 不同周期的滤波参数建议
        filter_configs = {
            '1m': {'window': 3, 'type': 'sma'},   # 短周期用小窗口
            '5m': {'window': 3, 'type': 'sma'},   # 短周期用小窗口
            '15m': {'window': 5, 'type': 'sma'},  # 中等窗口
            '30m': {'window': 5, 'type': 'sma'},  # 中等窗口
            '1h': {'window': 5, 'type': 'sma'},   # 中等窗口
            '1d': {'window': 7, 'type': 'sma'},   # 长周期用大窗口
        }
        
        return filter_configs.get(period_key, {'window': 5, 'type': 'sma'})
    
    @classmethod
    def print_all_periods(cls):
        """打印所有支持的周期信息"""
        print("="*80)
        print("📊 支持的交易周期配置")
        print("="*80)
        
        for period_key, period_info in cls.SUPPORTED_PERIODS.items():
            print(f"🔹 {period_key}: {period_info['name']}")
            print(f"   描述: {period_info['description']}")
            print(f"   更新间隔: {period_info['seconds']}秒")
            print(f"   推荐回看: {period_info['recommended_lookback']}个周期")
            print(f"   最少数据: {period_info['min_data_points']}个点")
            
            # 获取推荐配置
            recommended = cls.get_recommended_config(period_key)
            macd_config = recommended['macd_config']
            filter_config = recommended['filter_config']
            
            print(f"   MACD参数: 快线{macd_config['fast']}, 慢线{macd_config['slow']}, 信号线{macd_config['signal']}")
            print(f"   滤波参数: 窗口{filter_config['window']}, 类型{filter_config['type']}")
            print()
        
        print("="*80)
        print("💡 使用建议:")
        print("- 1分钟/5分钟: 适合超短线交易，需要快速反应")
        print("- 15分钟/30分钟: 适合日内交易，平衡反应速度和稳定性")
        print("- 1小时: 适合中线交易，信号相对稳定")
        print("- 1天: 适合长线交易，信号最稳定但反应较慢")
        print("="*80)


# 预定义的交易配置模板
TRADING_TEMPLATES = {
    'scalping': {
        'name': '超短线交易',
        'period': '1m',
        'description': '1分钟周期，适合快速进出',
        'risk_level': 'high',
    },
    'day_trading': {
        'name': '日内交易',
        'period': '15m',
        'description': '15分钟周期，适合日内操作',
        'risk_level': 'medium',
    },
    'swing_trading': {
        'name': '波段交易',
        'period': '1h',
        'description': '1小时周期，适合中短线操作',
        'risk_level': 'medium',
    },
    'position_trading': {
        'name': '趋势交易',
        'period': '1d',
        'description': '日线周期，适合长线持有',
        'risk_level': 'low',
    },
}


def get_trading_template(template_name):
    """
    获取交易模板配置
    
    Args:
        template_name (str): 模板名称
        
    Returns:
        dict: 完整的交易配置
    """
    if template_name not in TRADING_TEMPLATES:
        raise ValueError(f"不支持的交易模板: {template_name}. 支持的模板: {list(TRADING_TEMPLATES.keys())}")
    
    template = TRADING_TEMPLATES[template_name]
    period = template['period']
    
    # 获取周期的推荐配置
    recommended_config = TradingPeriodConfig.get_recommended_config(period)
    
    # 合并模板和推荐配置
    full_config = {
        **template,
        **recommended_config,
        'template_name': template_name,
    }
    
    return full_config


def print_trading_templates():
    """打印所有交易模板"""
    print("="*80)
    print("📋 预定义交易模板")
    print("="*80)
    
    for template_name, template_info in TRADING_TEMPLATES.items():
        print(f"🎯 {template_name}: {template_info['name']}")
        print(f"   周期: {template_info['period']}")
        print(f"   描述: {template_info['description']}")
        print(f"   风险等级: {template_info['risk_level']}")
        print()
    
    print("="*80)


if __name__ == "__main__":
    # 演示配置功能
    print("🔧 交易周期配置演示")
    
    # 显示所有支持的周期
    TradingPeriodConfig.print_all_periods()
    
    # 显示交易模板
    print_trading_templates()
    
    # 演示获取特定周期的配置
    print("📝 获取1小时周期的推荐配置:")
    config = TradingPeriodConfig.get_recommended_config('1h')
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\n📝 获取日内交易模板配置:")
    template_config = get_trading_template('day_trading')
    for key, value in template_config.items():
        print(f"  {key}: {value}")
