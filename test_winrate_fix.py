"""
胜率问题修复测试脚本
==================

测试修复后的MACD策略，验证：
1. 伪装模式已关闭，显示正常金融术语
2. 胜率计算是否正确
3. 交易逻辑是否合理
"""

from datetime import datetime
from lB_BT_Plotly import BacktestSystem
from longport.openapi import Period

def test_winrate_fix():
    """
    测试胜率修复
    """
    print("="*60)
    print("胜率问题修复测试")
    print("="*60)
    
    # 创建回测系统（关闭伪装模式）
    system = BacktestSystem(enable_cache=True, disguise_mode=False)
    
    # 设置测试参数
    symbol = "AAPL.US"  # 苹果股票
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)  # 使用更长的时间段
    initial_cash = 100000  # 10万美元初始资金
    
    print(f"测试股票: {symbol}")
    print(f"测试时间: {start_date.date()} 到 {end_date.date()}")
    print(f"初始资金: ${initial_cash:,}")
    print(f"伪装模式: 已关闭（显示正常金融术语）")
    print("-"*60)
    
    # 运行回测
    results = system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash,
        period=Period.Day
    )
    
    if results:
        print("\n" + "="*60)
        print("修复后的回测结果")
        print("="*60)
        
        # 显示关键指标
        print(f"📊 交易统计:")
        print(f"   总交易次数: {results['trade_count']}")
        print(f"   盈利交易: {results['win_count']}")
        print(f"   亏损交易: {results['lose_count']}")
        print(f"   胜率: {results['win_rate']:.1f}%")
        
        print(f"\n💰 收益指标:")
        print(f"   总收益率: {results['total_return']:.2f}%")
        print(f"   最大回撤: {results['max_drawdown']:.2f}%")
        print(f"   夏普比率: {results['sharpe_ratio']:.4f}" if results['sharpe_ratio'] else "   夏普比率: N/A")
        
        print(f"\n💵 资金变化:")
        print(f"   初始资金: ${results['initial_cash']:,.2f}")
        print(f"   最终资金: ${results['end_value']:,.2f}")
        print(f"   净利润: ${results['end_value'] - results['initial_cash']:,.2f}")
        
        # 验证胜率是否合理
        print(f"\n🔍 胜率验证:")
        if results['trade_count'] > 0:
            calculated_winrate = (results['win_count'] / results['trade_count']) * 100
            print(f"   手动计算胜率: {results['win_count']}/{results['trade_count']} = {calculated_winrate:.1f}%")
            print(f"   系统计算胜率: {results['win_rate']:.1f}%")
            
            if abs(calculated_winrate - results['win_rate']) < 0.1:
                print(f"   ✅ 胜率计算正确")
            else:
                print(f"   ❌ 胜率计算有误")
                
            # 检查胜率是否合理
            if results['win_rate'] == 100.0:
                print(f"   ⚠️  警告: 胜率100%不太现实，可能存在问题")
            elif results['win_rate'] == 0.0:
                print(f"   ⚠️  警告: 胜率0%，策略可能有问题")
            else:
                print(f"   ✅ 胜率看起来合理")
        else:
            print(f"   ⚠️  没有完成的交易")
        
        # 检查交易记录
        strategy = results['strategy']
        if hasattr(strategy, 'trades') and strategy.trades:
            print(f"\n📋 交易记录详情:")
            for i, trade in enumerate(strategy.trades[:5], 1):  # 只显示前5笔交易
                profit_loss = "盈利" if trade['pnlcomm'] > 0 else "亏损"
                print(f"   交易 {i}: {trade['date']} | {profit_loss} ${trade['pnlcomm']:.2f}")
            
            if len(strategy.trades) > 5:
                print(f"   ... 还有 {len(strategy.trades) - 5} 笔交易")
        
        return results
    else:
        print("❌ 回测失败，请检查数据和网络连接")
        return None

def test_different_symbols():
    """
    测试不同股票的胜率
    """
    print("\n" + "="*60)
    print("多股票胜率测试")
    print("="*60)
    
    symbols = ["AAPL.US", "MSFT.US", "GOOGL.US"]
    system = BacktestSystem(enable_cache=True, disguise_mode=False)
    
    results_summary = []
    
    for symbol in symbols:
        print(f"\n测试股票: {symbol}")
        
        results = system.run_backtest(
            symbol=symbol,
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 6, 30),
            initial_cash=100000,
            period=Period.Day
        )
        
        if results:
            summary = {
                "股票": symbol,
                "交易次数": results['trade_count'],
                "胜率": f"{results['win_rate']:.1f}%",
                "收益率": f"{results['total_return']:.2f}%",
                "盈利交易": results['win_count'],
                "亏损交易": results['lose_count']
            }
            results_summary.append(summary)
    
    # 打印汇总
    if results_summary:
        print(f"\n" + "="*60)
        print("多股票胜率汇总")
        print("="*60)
        print(f"{'股票':<10} {'交易次数':<8} {'胜率':<8} {'收益率':<10} {'盈利':<6} {'亏损':<6}")
        print("-" * 60)
        
        for summary in results_summary:
            print(f"{summary['股票']:<10} {summary['交易次数']:<8} {summary['胜率']:<8} "
                  f"{summary['收益率']:<10} {summary['盈利交易']:<6} {summary['亏损交易']:<6}")

def main():
    """
    主函数
    """
    try:
        # 测试1：基本胜率修复
        test_winrate_fix()
        
        # 测试2：多股票测试
        test_different_symbols()
        
        print("\n" + "="*60)
        print("测试总结")
        print("="*60)
        print("✅ 伪装模式已关闭，显示正常金融术语")
        print("✅ 添加了持仓状态检查，避免重复买入/卖出")
        print("✅ 添加了详细的调试信息")
        print("✅ 胜率计算逻辑已优化")
        print("\n如果胜率仍然显示100%，可能的原因：")
        print("1. 测试时间段内策略确实表现很好")
        print("2. 交易信号生成逻辑需要进一步调整")
        print("3. 数据质量或市场条件特殊")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
