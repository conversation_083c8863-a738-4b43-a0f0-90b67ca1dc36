"""
交易周期配置演示
===============

演示如何使用可配置的交易周期功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_period_config import TradingPeriodConfig, get_trading_template, print_trading_templates
from macd_histogram_live_trading import LiveTradingSystem

def demo_period_configuration():
    """演示周期配置功能"""
    print("="*80)
    print("🎯 MACD交易系统 - 周期配置演示")
    print("="*80)
    
    # 1. 显示所有支持的周期
    print("\n📊 第一步：查看所有支持的交易周期")
    TradingPeriodConfig.print_all_periods()
    
    # 2. 显示预定义模板
    print("\n📋 第二步：查看预定义交易模板")
    print_trading_templates()
    
    # 3. 演示不同周期的配置
    print("\n🔧 第三步：演示不同周期的配置")
    periods_to_demo = ['1m', '15m', '1h', '1d']
    
    for period in periods_to_demo:
        print(f"\n🔹 {period} 周期配置:")
        config = TradingPeriodConfig.get_recommended_config(period)
        period_info = TradingPeriodConfig.get_period_info(period)
        
        print(f"   名称: {period_info['name']}")
        print(f"   描述: {period_info['description']}")
        print(f"   更新间隔: {config['update_interval']}秒")
        print(f"   回看周期: {config['lookback_periods']}")
        print(f"   MACD参数: {config['macd_config']}")
        print(f"   滤波参数: {config['filter_config']}")
    
    # 4. 演示模板配置
    print("\n🎯 第四步：演示交易模板配置")
    templates_to_demo = ['scalping', 'day_trading', 'swing_trading', 'position_trading']
    
    for template_name in templates_to_demo:
        print(f"\n🔸 {template_name} 模板:")
        template_config = get_trading_template(template_name)
        
        print(f"   名称: {template_config['name']}")
        print(f"   周期: {template_config['period']}")
        print(f"   描述: {template_config['description']}")
        print(f"   风险等级: {template_config['risk_level']}")
        print(f"   更新间隔: {template_config['update_interval']}秒")

def demo_trading_system_creation():
    """演示如何创建不同周期的交易系统"""
    print("\n" + "="*80)
    print("🚀 第五步：演示创建不同周期的交易系统")
    print("="*80)
    
    # 演示创建不同周期的交易系统（不实际运行）
    demo_configs = [
        {'period': '5m', 'symbol': 'AAPL.US', 'description': '5分钟短线交易'},
        {'period': '1h', 'symbol': 'TSLA.US', 'description': '1小时中线交易'},
        {'period': '1d', 'symbol': 'SPY.US', 'description': '日线长线交易'},
    ]
    
    for config in demo_configs:
        print(f"\n🔧 创建 {config['description']} 系统:")
        print(f"   标的: {config['symbol']}")
        print(f"   周期: {config['period']}")
        
        # 获取周期信息
        period_info = TradingPeriodConfig.get_period_info(config['period'])
        print(f"   周期名称: {period_info['name']}")
        print(f"   更新间隔: {period_info['seconds']}秒")
        
        # 这里只是演示创建过程，不实际运行
        print(f"   创建代码示例:")
        print(f"   trading_system = LiveTradingSystem(")
        print(f"       symbol='{config['symbol']}',")
        print(f"       position_size=1,")
        print(f"       period='{config['period']}'")
        print(f"   )")
        print(f"   ✅ 系统配置完成（演示模式）")

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "="*80)
    print("📝 使用示例代码")
    print("="*80)
    
    examples = [
        {
            'title': '1. 使用预定义模板创建交易系统',
            'code': '''
# 使用日内交易模板
template_config = get_trading_template('day_trading')
trading_system = LiveTradingSystem(
    symbol="AAPL.US",
    position_size=1,
    period=template_config['period'],  # 15分钟
    update_interval=template_config['update_interval']
)
'''
        },
        {
            'title': '2. 自定义周期创建交易系统',
            'code': '''
# 自定义5分钟交易系统
trading_system = LiveTradingSystem(
    symbol="TSLA.US",
    position_size=2,
    period='5m',  # 5分钟周期
    update_interval=None  # 自动根据周期设置
)
'''
        },
        {
            'title': '3. 获取周期推荐配置',
            'code': '''
# 获取1小时周期的推荐配置
config = TradingPeriodConfig.get_recommended_config('1h')
print(f"推荐MACD参数: {config['macd_config']}")
print(f"推荐滤波参数: {config['filter_config']}")
print(f"推荐回看周期: {config['lookback_periods']}")
'''
        },
        {
            'title': '4. 验证周期支持',
            'code': '''
# 验证周期是否支持
period = '30m'
if TradingPeriodConfig.validate_period(period):
    period_info = TradingPeriodConfig.get_period_info(period)
    print(f"支持 {period}: {period_info['name']}")
else:
    print(f"不支持周期: {period}")
'''
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{example['title']}:")
        print(f"```python{example['code']}```")

def main():
    """主演示函数"""
    try:
        # 运行所有演示
        demo_period_configuration()
        demo_trading_system_creation()
        show_usage_examples()
        
        print("\n" + "="*80)
        print("🎉 演示完成！")
        print("="*80)
        print("💡 要点总结:")
        print("1. 支持6种交易周期：1m, 5m, 15m, 30m, 1h, 1d")
        print("2. 提供4种预定义交易模板")
        print("3. 每个周期都有推荐的MACD和滤波参数")
        print("4. 更新间隔可以自动根据周期设置")
        print("5. 所有配置都可以自定义覆盖")
        print("\n⚠️  注意:")
        print("- 短周期（1m, 5m）适合超短线，但信号噪音较多")
        print("- 中周期（15m, 30m, 1h）平衡了反应速度和稳定性")
        print("- 长周期（1d）信号最稳定，但反应较慢")
        print("- 实盘使用前请充分回测验证")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
