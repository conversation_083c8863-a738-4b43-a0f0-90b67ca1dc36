"""
基于MACD直方图差分的实盘交易系统
==================================

从Longbridge获取1小时数据，计算MACD直方图，进行滤波处理，
基于直方图差分符号变化进行交易决策，每次交易1手。

功能特点：
1. 实时获取1小时K线数据
2. 计算MACD直方图
3. 对直方图进行滤波处理
4. 计算差分并检测符号变化
5. 自动执行交易（每次1手）
6. 完整的风险控制和日志记录
"""

import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import threading
import signal
import sys
from scipy import signal as scipy_signal

# Longbridge API导入
from longport.openapi import QuoteContext, TradeContext, Config, Period, AdjustType
from longport.openapi import OrderSide, OrderType, TimeInForceType

class MACDHistogramStrategy:
    """
    MACD直方图差分交易策略
    =====================
    
    基于MACD直方图的差分符号变化进行交易决策
    """
    
    def __init__(self, symbol="YINN.US", position_size=1):
        """
        初始化策略
        
        Args:
            symbol (str): 交易标的
            position_size (int): 每次交易数量（手）
        """
        self.symbol = symbol
        self.position_size = position_size
        
        # MACD参数
        self.fast_period = 12
        self.slow_period = 26
        self.signal_period = 9
        
        # 滤波参数
        self.filter_window = 5  # 滤波窗口长度
        
        # 数据存储
        self.price_data = pd.DataFrame()
        self.macd_data = pd.DataFrame()
        self.histogram_history = []
        self.filtered_histogram_history = []
        self.diff_history = []
        
        # 交易状态
        self.current_position = 0
        self.last_trade_time = None
        self.total_trades = 0
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 初始化Longbridge连接
        self._init_longbridge()
        
        self.logger.info(f"MACD直方图策略初始化完成 - 标的: {symbol}, 交易量: {position_size}手")
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('MACDHistogramStrategy')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(f'macd_trading_{datetime.now().strftime("%Y%m%d")}.log')
            file_handler.setLevel(logging.DEBUG)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化器
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
        
        return logger
    
    def _init_longbridge(self):
        """初始化Longbridge API连接"""
        try:
            # 从环境变量加载配置
            self.config = Config.from_env()
            
            # 创建行情上下文
            self.quote_ctx = QuoteContext(self.config)
            
            # 创建交易上下文
            self.trade_ctx = TradeContext(self.config)
            
            self.logger.info("Longbridge API连接成功")
            
        except Exception as e:
            self.logger.error(f"Longbridge API连接失败: {e}")
            raise
    
    def get_latest_data(self, lookback_hours=100):
        """
        获取最新的1小时K线数据
        
        Args:
            lookback_hours (int): 获取多少小时的历史数据
            
        Returns:
            pd.DataFrame: 价格数据
        """
        flag = True
        while flag:
            try:
                end_date = datetime.now().date()
                start_date = (datetime.now() - timedelta(days=lookback_hours//24 + 5)).date()
                
                self.logger.debug(f"获取数据: {self.symbol}, {start_date} 到 {end_date}")
                
                # 从Longbridge获取1小时K线数据
                resp = self.quote_ctx.history_candlesticks_by_date(
                    self.symbol,
                    Period.Min_60,  # 1小时K线
                    AdjustType.ForwardAdjust,
                    start_date,
                    end_date
                )
                
                if not resp:
                    self.logger.error(f"未能获取到 {self.symbol} 的数据")
                    return pd.DataFrame()
                
                # 转换为DataFrame
                data_list = []
                for candle in resp:
                    # 智能处理时间戳格式
                    timestamp = candle.timestamp
                    if isinstance(timestamp, (int, float)):
                        # 数值型时间戳，使用unit='s'
                        datetime_val = pd.to_datetime(timestamp, unit='s')
                    else:
                        # 字符串格式，直接转换
                        datetime_val = pd.to_datetime(timestamp)

                    data_list.append({
                        'datetime': datetime_val,
                        'open': float(candle.open),
                        'high': float(candle.high),
                        'low': float(candle.low),
                        'close': float(candle.close),
                        'volume': int(candle.volume)
                    })
                
                df = pd.DataFrame(data_list)
                df.set_index('datetime', inplace=True)
                df.sort_index(inplace=True)
                
                self.logger.info(f"成功获取 {len(df)} 条1小时K线数据")
                flag = False
                return df
                
            except Exception as e:
                self.logger.error(f"获取数据失败: {e}")
                flag = True
                return pd.DataFrame()
    
    def calculate_macd(self, data):
        """
        计算MACD指标
        
        Args:
            data (pd.DataFrame): 价格数据
            
        Returns:
            pd.DataFrame: MACD数据
        """
        if len(data) < self.slow_period:
            self.logger.warning("数据不足，无法计算MACD")
            return pd.DataFrame()
        
        try:
            close_prices = data['close']
            
            # 计算EMA
            ema_fast = close_prices.ewm(span=self.fast_period).mean()
            ema_slow = close_prices.ewm(span=self.slow_period).mean()
            
            # 计算MACD线
            macd_line = ema_fast - ema_slow
            
            # 计算信号线
            signal_line = macd_line.ewm(span=self.signal_period).mean()
            
            # 计算直方图
            histogram = macd_line - signal_line
            
            # 创建MACD数据框
            macd_df = pd.DataFrame({
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }, index=data.index)
            
            self.logger.debug("MACD指标计算完成")
            return macd_df
            
        except Exception as e:
            self.logger.error(f"MACD计算失败: {e}")
            return pd.DataFrame()
    
    def apply_filter(self, data_series):
        """
        对数据进行滤波处理
        
        Args:
            data_series (pd.Series or list): 原始数据
            
        Returns:
            list: 滤波后的数据
        """
        if len(data_series) < self.filter_window:
            return list(data_series)
        
        try:
            # 使用简单移动平均进行滤波
            data_array = np.array(data_series)
            filtered_data = []
            
            for i in range(len(data_array)):
                start_idx = max(0, i - self.filter_window + 1)
                window_data = data_array[start_idx:i+1]
                filtered_value = np.mean(window_data)
                filtered_data.append(filtered_value)
            
            self.logger.debug(f"滤波处理完成，窗口长度: {self.filter_window}")
            return filtered_data
            
        except Exception as e:
            self.logger.error(f"滤波处理失败: {e}")
            return list(data_series)
    
    def generate_signal(self):
        """
        基于MACD直方图差分符号变化生成交易信号
        
        Returns:
            int: 交易信号 (1: 买入, -1: 卖出, 0: 无信号)
        """
        if len(self.filtered_histogram_history) < 3:
            return 0
        
        try:
            # 计算直方图的差分
            histogram_diff = np.diff(self.filtered_histogram_history)
            
            if len(histogram_diff) < 2:
                return 0
            
            # 获取最近两个差分值
            current_diff = histogram_diff[-1]
            previous_diff = histogram_diff[-2]
            
            # 记录差分历史
            self.diff_history.append(current_diff)
            if len(self.diff_history) > 50:
                self.diff_history = self.diff_history[-50:]
            
            # 设置最小阈值，避免噪音交易
            min_threshold = 0.001
            
            signal = 0
            
            # 买入信号：差分从负变正（直方图开始上升）
            if (previous_diff <= 0 and current_diff > 0 and 
                abs(current_diff) > min_threshold):
                signal = 1
                self.logger.info(f"🔵 买入信号: 差分从 {previous_diff:.6f} 变为 {current_diff:.6f}")
            
            # 卖出信号：差分从正变负（直方图开始下降）
            elif (previous_diff >= 0 and current_diff < 0 and 
                  abs(current_diff) > min_threshold):
                signal = -1
                self.logger.info(f"🔴 卖出信号: 差分从 {previous_diff:.6f} 变为 {current_diff:.6f}")
            
            return signal

        except Exception as e:
            self.logger.error(f"信号生成失败: {e}")
            return 0

    def execute_trade(self, signal):
        """
        执行交易

        Args:
            signal (int): 交易信号 (1: 买入, -1: 卖出)

        Returns:
            bool: 交易是否成功
        """
        if signal == 0:
            return False

        try:
            # 检查持仓限制
            if signal == 1 and self.current_position >= 10:  # 最大持仓限制
                self.logger.warning("已达到最大持仓限制，跳过买入")
                return False

            if signal == -1 and self.current_position <= 0:  # 无持仓时不能卖出
                self.logger.warning("当前无持仓，跳过卖出")
                return False

            # 获取当前价格
            latest_data = self.get_latest_data(lookback_hours=1)
            if latest_data.empty:
                self.logger.error("无法获取当前价格")
                return False

            current_price = latest_data['close'].iloc[-1]

            # 确定订单方向
            side = OrderSide.Buy if signal == 1 else OrderSide.Sell
            action = "买入" if signal == 1 else "卖出"

            self.logger.info(f"准备{action} {self.position_size}手 {self.symbol}，当前价格: ${current_price:.2f}")

            # 提交市价单
            order = self.trade_ctx.submit_order(
                symbol=self.symbol,
                order_type=OrderType.MO,  # 市价单
                side=side,
                submitted_quantity=self.position_size,
                time_in_force=TimeInForceType.Day,
                remark=f"MACD直方图策略-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )

            if order:
                # 更新持仓
                if signal == 1:
                    self.current_position += self.position_size
                else:
                    self.current_position -= self.position_size

                self.total_trades += 1
                self.last_trade_time = datetime.now()

                self.logger.info(f"✅ {action}订单已提交: {self.position_size}手, 订单ID: {order.order_id}")
                self.logger.info(f"当前持仓: {self.current_position}手, 总交易次数: {self.total_trades}")

                return True
            else:
                self.logger.error(f"❌ {action}订单提交失败")
                return False

        except Exception as e:
            self.logger.error(f"交易执行失败: {e}")
            return False

    def update_strategy(self):
        """
        更新策略数据和生成交易信号

        Returns:
            int: 交易信号
        """
        try:
            # 获取最新数据
            latest_data = self.get_latest_data()
            if latest_data.empty:
                self.logger.error("无法获取最新数据")
                return 0

            self.price_data = latest_data

            # 计算MACD
            self.macd_data = self.calculate_macd(self.price_data)
            if self.macd_data.empty:
                return 0

            # 更新直方图历史
            histogram_values = self.macd_data['histogram'].dropna().tolist()
            self.histogram_history = histogram_values

            # 对直方图进行滤波
            if len(self.histogram_history) >= self.filter_window:
                self.filtered_histogram_history = self.apply_filter(self.histogram_history)

                # 生成交易信号
                signal = self.generate_signal()

                # 打印当前状态
                if len(self.filtered_histogram_history) > 0:
                    current_histogram = self.filtered_histogram_history[-1]
                    self.logger.info(f"当前直方图值: {current_histogram:.6f}, 持仓: {self.current_position}手")

                return signal

            return 0

        except Exception as e:
            self.logger.error(f"策略更新失败: {e}")
            return 0

    def get_strategy_status(self):
        """获取策略状态信息"""
        return {
            'symbol': self.symbol,
            'current_position': self.current_position,
            'total_trades': self.total_trades,
            'last_trade_time': self.last_trade_time,
            'data_length': len(self.price_data),
            'histogram_length': len(self.histogram_history),
            'latest_histogram': self.histogram_history[-1] if self.histogram_history else None,
        }


class LiveTradingSystem:
    """
    实盘交易系统主类
    ================

    整合策略执行、数据更新、交易管理等功能
    """

    def __init__(self, symbol="AAPL.US", position_size=1, update_interval=3600):
        """
        初始化交易系统

        Args:
            symbol (str): 交易标的
            position_size (int): 每次交易数量
            update_interval (int): 数据更新间隔（秒），默认1小时
        """
        self.symbol = symbol
        self.position_size = position_size
        self.update_interval = update_interval
        self.running = False

        # 初始化策略
        self.strategy = MACDHistogramStrategy(symbol, position_size)

        # 设置日志
        self.logger = logging.getLogger('LiveTradingSystem')
        self.logger.setLevel(logging.INFO)

        # 信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info(f"实盘交易系统初始化完成 - {symbol}, 更新间隔: {update_interval}秒")

    def _signal_handler(self, signum, frame):
        """处理退出信号"""
        self.logger.info("收到退出信号，正在安全关闭系统...")
        self.stop()
        sys.exit(0)

    def start(self):
        """启动交易系统"""
        self.logger.info("🚀 启动实盘交易系统...")
        self.running = True

        # 首次更新数据
        self.logger.info("执行首次数据更新...")
        signal = self.strategy.update_strategy()
        if signal != 0:
            self.strategy.execute_trade(signal)

        # 主循环
        while self.running:
            try:
                self.logger.info(f"等待 {self.update_interval} 秒后进行下次更新...")
                time.sleep(self.update_interval)

                if not self.running:
                    break

                self.logger.info("🔄 更新策略数据...")
                signal = self.strategy.update_strategy()

                if signal != 0:
                    self.logger.info(f"检测到交易信号: {signal}")
                    success = self.strategy.execute_trade(signal)
                    if success:
                        self.logger.info("✅ 交易执行成功")
                    else:
                        self.logger.warning("⚠️ 交易执行失败")
                else:
                    self.logger.info("无交易信号")

                # 打印策略状态
                status = self.strategy.get_strategy_status()
                self.logger.info(f"策略状态: 持仓={status['current_position']}手, "
                               f"总交易={status['total_trades']}次")

            except KeyboardInterrupt:
                self.logger.info("用户中断，正在退出...")
                break
            except Exception as e:
                self.logger.error(f"主循环错误: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

        self.logger.info("交易系统已停止")

    def stop(self):
        """停止交易系统"""
        self.running = False
        self.logger.info("交易系统停止信号已发送")

    def get_system_status(self):
        """获取系统状态"""
        strategy_status = self.strategy.get_strategy_status()
        return {
            'running': self.running,
            'update_interval': self.update_interval,
            'strategy_status': strategy_status
        }


def main():
    """
    主函数 - 启动实盘交易系统
    """
    print("="*60)
    print("🎯 MACD直方图差分实盘交易系统")
    print("="*60)
    print("功能说明:")
    print("1. 从Longbridge获取1小时K线数据")
    print("2. 计算MACD直方图并进行滤波")
    print("3. 基于直方图差分符号变化生成交易信号")
    print("4. 自动执行交易（每次1手）")
    print("5. 实时监控和日志记录")
    print("="*60)

    # 配置参数
    SYMBOL = "YINN.US"  # 交易标的，可以修改
    POSITION_SIZE = 1   # 每次交易数量（手）
    UPDATE_INTERVAL = 3600  # 更新间隔（秒），1小时 = 3600秒

    print(f"交易配置:")
    print(f"  标的: {SYMBOL}")
    print(f"  交易量: {POSITION_SIZE}手")
    print(f"  更新间隔: {UPDATE_INTERVAL}秒 ({UPDATE_INTERVAL//3600}小时)")
    print("="*60)

    try:
        # 创建交易系统
        trading_system = LiveTradingSystem(
            symbol=SYMBOL,
            position_size=POSITION_SIZE,
            update_interval=UPDATE_INTERVAL
        )

        print("✅ 交易系统初始化成功")
        print("⚠️  注意: 这是实盘交易系统，请确保:")
        print("   1. 已正确配置Longbridge API密钥")
        print("   2. 账户有足够资金")
        print("   3. 了解交易风险")
        print("="*60)

        # 等待用户确认
        user_input = input("确认启动实盘交易? (输入 'yes' 确认): ")
        if user_input.lower() != 'yes':
            print("❌ 用户取消，系统退出")
            return

        print("🚀 启动交易系统...")
        trading_system.start()

    except KeyboardInterrupt:
        print("\n❌ 用户中断")
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        logging.error(f"系统错误: {e}")
    finally:
        print("👋 交易系统已退出")


if __name__ == "__main__":
    main()
