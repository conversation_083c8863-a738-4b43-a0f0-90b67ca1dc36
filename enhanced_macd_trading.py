"""
增强版MACD直方图实盘交易系统
============================

基于配置文件的完整实盘交易系统，支持：
1. 从Longbridge获取1小时K线数据
2. 计算MACD直方图并进行滤波
3. 基于直方图差分符号变化生成交易信号
4. 自动执行交易（每次1手）
5. 完整的风险控制和监控
6. 灵活的配置管理
"""

import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import threading
import signal
import sys
import os

# Longbridge API导入
from longport.openapi import QuoteContext, TradeContext, Config, Period, AdjustType
from longport.openapi import OrderSide, OrderType, TimeInForceType

# 导入配置
from macd_trading_config import (
    get_full_config, validate_config, print_config,
    TRADING_CONFIG, MACD_CONFIG, FILTER_CONFIG, SIGNAL_CONFIG,
    RISK_CONFIG, DATA_CONFIG, LOGGING_CONFIG
)

class EnhancedMACDStrategy:
    """
    增强版MACD直方图差分交易策略
    ===========================
    
    基于配置文件的完整策略实现
    """
    
    def __init__(self, config=None):
        """
        初始化策略
        
        Args:
            config (dict, optional): 策略配置，如果不提供则使用默认配置
        """
        self.config = config or get_full_config()
        
        # 提取各模块配置
        self.trading_config = self.config['trading']
        self.macd_config = self.config['macd']
        self.filter_config = self.config['filter']
        self.signal_config = self.config['signal']
        self.risk_config = self.config['risk']
        self.data_config = self.config['data']
        
        # 基本参数
        self.symbol = self.trading_config['symbol']
        self.position_size = self.trading_config['position_size']
        
        # 数据存储
        self.price_data = pd.DataFrame()
        self.macd_data = pd.DataFrame()
        self.histogram_history = []
        self.filtered_histogram_history = []
        self.diff_history = []
        
        # 交易状态
        self.current_position = 0
        self.last_trade_time = None
        self.total_trades = 0
        self.daily_trades = 0
        self.last_trade_date = None
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 初始化Longbridge连接
        self._init_longbridge()
        
        self.logger.info(f"增强版MACD策略初始化完成 - {self.symbol}")
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('EnhancedMACDStrategy')
        logger.setLevel(getattr(logging, LOGGING_CONFIG['log_level']))
        
        if not logger.handlers:
            # 文件处理器
            if LOGGING_CONFIG['log_to_file']:
                log_filename = f"{LOGGING_CONFIG['log_file_prefix']}_{datetime.now().strftime('%Y%m%d')}.log"
                file_handler = logging.FileHandler(log_filename)
                file_handler.setLevel(logging.DEBUG)
                
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
            
            # 控制台处理器
            if LOGGING_CONFIG['log_to_console']:
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.INFO)
                
                formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                console_handler.setFormatter(formatter)
                logger.addHandler(console_handler)
        
        return logger
    
    def _init_longbridge(self):
        """初始化Longbridge API连接"""
        try:
            # 从环境变量加载配置
            self.longbridge_config = Config.from_env()
            
            # 创建行情上下文
            self.quote_ctx = QuoteContext(self.longbridge_config)
            
            # 创建交易上下文
            self.trade_ctx = TradeContext(self.longbridge_config)
            
            self.logger.info("Longbridge API连接成功")
            
        except Exception as e:
            self.logger.error(f"Longbridge API连接失败: {e}")
            raise
    
    def get_latest_data(self):
        """获取最新的1小时K线数据"""
        try:
            end_date = datetime.now().date()
            lookback_days = self.data_config['lookback_hours'] // 24 + 5
            start_date = (datetime.now() - timedelta(days=lookback_days)).date()
            
            self.logger.debug(f"获取数据: {self.symbol}, {start_date} 到 {end_date}")
            
            # 从Longbridge获取数据
            resp = self.quote_ctx.history_candlesticks_by_date(
                self.symbol,
                self.data_config['period'],
                AdjustType.ForwardAdjust,
                start_date,
                end_date
            )
            
            if not resp:
                self.logger.error(f"未能获取到 {self.symbol} 的数据")
                return pd.DataFrame()
            
            # 转换为DataFrame
            data_list = []
            for candle in resp:
                data_list.append({
                    'datetime': pd.to_datetime(candle.timestamp, unit='s'),
                    'open': float(candle.open),
                    'high': float(candle.high),
                    'low': float(candle.low),
                    'close': float(candle.close),
                    'volume': int(candle.volume)
                })
            
            df = pd.DataFrame(data_list)
            df.set_index('datetime', inplace=True)
            df.sort_index(inplace=True)
            
            self.logger.info(f"成功获取 {len(df)} 条1小时K线数据")
            return df
            
        except Exception as e:
            self.logger.error(f"获取数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_macd(self, data):
        """计算MACD指标"""
        if len(data) < self.macd_config['slow_period']:
            self.logger.warning("数据不足，无法计算MACD")
            return pd.DataFrame()
        
        try:
            close_prices = data['close']
            
            # 计算EMA
            ema_fast = close_prices.ewm(span=self.macd_config['fast_period']).mean()
            ema_slow = close_prices.ewm(span=self.macd_config['slow_period']).mean()
            
            # 计算MACD线
            macd_line = ema_fast - ema_slow
            
            # 计算信号线
            signal_line = macd_line.ewm(span=self.macd_config['signal_period']).mean()
            
            # 计算直方图
            histogram = macd_line - signal_line
            
            # 创建MACD数据框
            macd_df = pd.DataFrame({
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }, index=data.index)
            
            self.logger.debug("MACD指标计算完成")
            return macd_df
            
        except Exception as e:
            self.logger.error(f"MACD计算失败: {e}")
            return pd.DataFrame()
    
    def apply_filter(self, data_series):
        """对数据进行滤波处理"""
        filter_window = self.filter_config['filter_window']
        filter_type = self.filter_config['filter_type']
        
        if len(data_series) < filter_window:
            return list(data_series)
        
        try:
            data_array = np.array(data_series)
            
            if filter_type == 'sma':
                # 简单移动平均滤波
                filtered_data = []
                for i in range(len(data_array)):
                    start_idx = max(0, i - filter_window + 1)
                    window_data = data_array[start_idx:i+1]
                    filtered_value = np.mean(window_data)
                    filtered_data.append(filtered_value)
                return filtered_data
                
            elif filter_type == 'ema':
                # 指数移动平均滤波
                alpha = self.filter_config['ema_alpha']
                filtered_data = [data_array[0]]
                for i in range(1, len(data_array)):
                    filtered_value = alpha * data_array[i] + (1 - alpha) * filtered_data[-1]
                    filtered_data.append(filtered_value)
                return filtered_data
            
            else:
                self.logger.warning(f"未知的滤波类型: {filter_type}")
                return list(data_series)
                
        except Exception as e:
            self.logger.error(f"滤波处理失败: {e}")
            return list(data_series)
    
    def generate_signal(self):
        """基于MACD直方图差分符号变化生成交易信号"""
        if len(self.filtered_histogram_history) < 3:
            return 0
        
        try:
            # 计算直方图的差分
            histogram_diff = np.diff(self.filtered_histogram_history)
            
            if len(histogram_diff) < 2:
                return 0
            
            # 获取最近两个差分值
            current_diff = histogram_diff[-1]
            previous_diff = histogram_diff[-2]
            
            # 记录差分历史
            self.diff_history.append(current_diff)
            if len(self.diff_history) > 50:
                self.diff_history = self.diff_history[-50:]
            
            # 使用配置的最小阈值
            min_threshold = self.signal_config['min_threshold']
            
            signal = 0
            
            # 买入信号：差分从负变正（直方图开始上升）
            if (previous_diff <= 0 and current_diff > 0 and 
                abs(current_diff) > min_threshold):
                signal = 1
                self.logger.info(f"🔵 买入信号: 差分从 {previous_diff:.6f} 变为 {current_diff:.6f}")
            
            # 卖出信号：差分从正变负（直方图开始下降）
            elif (previous_diff >= 0 and current_diff < 0 and 
                  abs(current_diff) > min_threshold):
                signal = -1
                self.logger.info(f"🔴 卖出信号: 差分从 {previous_diff:.6f} 变为 {current_diff:.6f}")
            
            return signal

        except Exception as e:
            self.logger.error(f"信号生成失败: {e}")
            return 0

    def can_trade(self, signal):
        """检查是否可以交易（风险控制）"""
        current_time = datetime.now()

        # 检查是否启用风险控制
        if not self.risk_config['enable_risk_control']:
            return True, "风险控制已禁用"

        # 检查交易时间
        trading_start = datetime.strptime(self.risk_config['trading_start_time'], "%H:%M").time()
        trading_end = datetime.strptime(self.risk_config['trading_end_time'], "%H:%M").time()

        if not (trading_start <= current_time.time() <= trading_end):
            return False, "不在交易时间内"

        # 检查每日交易次数
        if self.last_trade_date != current_time.date():
            self.daily_trades = 0
            self.last_trade_date = current_time.date()

        if self.daily_trades >= self.risk_config['max_daily_trades']:
            return False, "已达到每日最大交易次数"

        # 检查持仓限制
        if signal == 1:  # 买入信号
            if self.current_position >= self.trading_config['max_position']:
                return False, "已达到最大持仓限制"
        elif signal == -1:  # 卖出信号
            if self.current_position <= 0:
                return False, "当前无持仓"

        return True, "可以交易"

    def execute_trade(self, signal):
        """执行交易"""
        if signal == 0:
            return False

        # 风险控制检查
        can_trade, reason = self.can_trade(signal)
        if not can_trade:
            self.logger.warning(f"无法交易: {reason}")
            return False

        try:
            # 获取当前价格
            latest_data = self.get_latest_data()
            if latest_data.empty:
                self.logger.error("无法获取当前价格")
                return False

            current_price = latest_data['close'].iloc[-1]

            # 确定订单方向
            side = OrderSide.Buy if signal == 1 else OrderSide.Sell
            action = "买入" if signal == 1 else "卖出"

            self.logger.info(f"准备{action} {self.position_size}手 {self.symbol}，当前价格: ${current_price:.2f}")

            # 提交市价单
            order = self.trade_ctx.submit_order(
                symbol=self.symbol,
                order_type=OrderType.MO,  # 市价单
                side=side,
                submitted_quantity=self.position_size,
                time_in_force=TimeInForceType.Day,
                remark=f"MACD直方图策略-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )

            if order:
                # 更新持仓和交易统计
                if signal == 1:
                    self.current_position += self.position_size
                else:
                    self.current_position -= self.position_size

                self.total_trades += 1
                self.daily_trades += 1
                self.last_trade_time = datetime.now()

                self.logger.info(f"✅ {action}订单已提交: {self.position_size}手, 订单ID: {order.order_id}")
                self.logger.info(f"当前持仓: {self.current_position}手, 总交易次数: {self.total_trades}")

                return True
            else:
                self.logger.error(f"❌ {action}订单提交失败")
                return False

        except Exception as e:
            self.logger.error(f"交易执行失败: {e}")
            return False

    def update_strategy(self):
        """更新策略数据和生成交易信号"""
        try:
            # 获取最新数据
            latest_data = self.get_latest_data()
            if latest_data.empty:
                self.logger.error("无法获取最新数据")
                return 0

            self.price_data = latest_data

            # 计算MACD
            self.macd_data = self.calculate_macd(self.price_data)
            if self.macd_data.empty:
                return 0

            # 更新直方图历史
            histogram_values = self.macd_data['histogram'].dropna().tolist()
            self.histogram_history = histogram_values

            # 对直方图进行滤波
            if len(self.histogram_history) >= self.filter_config['filter_window']:
                self.filtered_histogram_history = self.apply_filter(self.histogram_history)

                # 生成交易信号
                signal = self.generate_signal()

                # 打印当前状态
                if len(self.filtered_histogram_history) > 0:
                    current_histogram = self.filtered_histogram_history[-1]
                    self.logger.info(f"当前直方图值: {current_histogram:.6f}, 持仓: {self.current_position}手")

                return signal

            return 0

        except Exception as e:
            self.logger.error(f"策略更新失败: {e}")
            return 0

    def get_strategy_status(self):
        """获取策略状态信息"""
        return {
            'symbol': self.symbol,
            'current_position': self.current_position,
            'total_trades': self.total_trades,
            'daily_trades': self.daily_trades,
            'last_trade_time': self.last_trade_time,
            'data_length': len(self.price_data),
            'histogram_length': len(self.histogram_history),
            'latest_histogram': self.histogram_history[-1] if self.histogram_history else None,
        }


def main():
    """主函数 - 启动增强版实盘交易系统"""
    print("="*70)
    print("🎯 增强版MACD直方图差分实盘交易系统")
    print("="*70)

    # 验证配置
    is_valid, errors = validate_config()
    if not is_valid:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   - {error}")
        return

    print("✅ 配置验证通过")

    # 打印配置信息
    print_config()

    try:
        # 创建策略实例
        strategy = EnhancedMACDStrategy()

        print("✅ 策略初始化成功")
        print("⚠️  注意: 这是实盘交易系统，请确保:")
        print("   1. 已正确配置Longbridge API密钥")
        print("   2. 账户有足够资金")
        print("   3. 了解交易风险")
        print("="*70)

        # 等待用户确认
        user_input = input("确认启动实盘交易? (输入 'yes' 确认): ")
        if user_input.lower() != 'yes':
            print("❌ 用户取消，系统退出")
            return

        print("🚀 启动交易系统...")

        # 主循环
        update_interval = TRADING_CONFIG['update_interval']

        while True:
            try:
                print(f"\n🔄 更新策略数据... ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")

                # 更新策略并生成信号
                signal = strategy.update_strategy()

                if signal != 0:
                    print(f"检测到交易信号: {signal}")
                    success = strategy.execute_trade(signal)
                    if success:
                        print("✅ 交易执行成功")
                    else:
                        print("⚠️ 交易执行失败")
                else:
                    print("无交易信号")

                # 打印策略状态
                status = strategy.get_strategy_status()
                print(f"策略状态: 持仓={status['current_position']}手, "
                      f"总交易={status['total_trades']}次, "
                      f"今日交易={status['daily_trades']}次")

                print(f"等待 {update_interval} 秒后进行下次更新...")
                time.sleep(update_interval)

            except KeyboardInterrupt:
                print("\n❌ 用户中断，正在退出...")
                break
            except Exception as e:
                print(f"❌ 主循环错误: {e}")
                logging.error(f"主循环错误: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

    except Exception as e:
        print(f"❌ 系统错误: {e}")
        logging.error(f"系统错误: {e}")
    finally:
        print("👋 交易系统已退出")


if __name__ == "__main__":
    main()
