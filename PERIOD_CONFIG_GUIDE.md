# MACD交易系统 - 可配置交易周期使用指南

## 🎯 概述

MACD直方图差分交易系统现在支持多种交易周期，用户可以根据自己的交易风格和策略需求选择合适的时间周期。

## 📊 支持的交易周期

| 周期代码 | 周期名称 | 更新间隔 | 适用场景 | 风险等级 |
|---------|---------|---------|---------|---------|
| `1m` | 1分钟 | 60秒 | 超短线交易 | 高 |
| `5m` | 5分钟 | 300秒 | 短线交易 | 高 |
| `15m` | 15分钟 | 900秒 | 日内交易 | 中 |
| `30m` | 30分钟 | 1800秒 | 中短线交易 | 中 |
| `1h` | 1小时 | 3600秒 | 中线交易 | 中 |
| `1d` | 1天 | 86400秒 | 长线交易 | 低 |

## 🎯 预定义交易模板

系统提供4种预定义的交易模板，每种模板都针对特定的交易风格进行了优化：

### 1. 超短线交易 (scalping)
- **周期**: 1分钟
- **特点**: 快速进出，捕捉短期价格波动
- **风险**: 高
- **适合**: 经验丰富的短线交易者

### 2. 日内交易 (day_trading)
- **周期**: 15分钟
- **特点**: 日内操作，平衡反应速度和稳定性
- **风险**: 中等
- **适合**: 日内交易者

### 3. 波段交易 (swing_trading)
- **周期**: 1小时
- **特点**: 中短线操作，信号相对稳定
- **风险**: 中等
- **适合**: 波段交易者

### 4. 趋势交易 (position_trading)
- **周期**: 1天
- **特点**: 长线持有，信号最稳定
- **风险**: 低
- **适合**: 长线投资者

## 🔧 使用方法

### 方法1: 使用预定义模板

```python
from trading_period_config import get_trading_template
from macd_histogram_live_trading import LiveTradingSystem

# 使用日内交易模板
template_config = get_trading_template('day_trading')

# 创建交易系统
trading_system = LiveTradingSystem(
    symbol="AAPL.US",
    position_size=1,
    period=template_config['period'],  # 15分钟
    update_interval=template_config['update_interval']
)

# 启动交易
trading_system.start()
```

### 方法2: 自定义周期配置

```python
from macd_histogram_live_trading import LiveTradingSystem

# 创建5分钟交易系统
trading_system = LiveTradingSystem(
    symbol="TSLA.US",
    position_size=2,
    period='5m',  # 5分钟周期
    update_interval=None  # 自动根据周期设置为300秒
)

# 启动交易
trading_system.start()
```

### 方法3: 完全自定义配置

```python
from macd_histogram_live_trading import LiveTradingSystem

# 完全自定义配置
trading_system = LiveTradingSystem(
    symbol="SPY.US",
    position_size=1,
    period='30m',  # 30分钟周期
    update_interval=1800  # 手动指定更新间隔
)

# 启动交易
trading_system.start()
```

## 📋 配置参数详解

### 周期配置参数

每个交易周期都包含以下配置参数：

```python
{
    'period': '1h',              # 周期代码
    'name': '1小时',             # 周期名称
    'seconds': 3600,             # 更新间隔（秒）
    'description': '1小时K线...',  # 描述
    'recommended_lookback': 100,  # 推荐回看周期数
    'min_data_points': 26,       # 最少数据点
}
```

### MACD参数配置

```python
{
    'fast': 12,    # 快线周期
    'slow': 26,    # 慢线周期
    'signal': 9    # 信号线周期
}
```

### 滤波参数配置

```python
{
    'window': 5,    # 滤波窗口大小
    'type': 'sma'   # 滤波类型 (sma/ema)
}
```

## 🛠️ 高级配置

### 获取推荐配置

```python
from trading_period_config import TradingPeriodConfig

# 获取1小时周期的推荐配置
config = TradingPeriodConfig.get_recommended_config('1h')

print(f"MACD参数: {config['macd_config']}")
print(f"滤波参数: {config['filter_config']}")
print(f"回看周期: {config['lookback_periods']}")
```

### 验证周期支持

```python
from trading_period_config import TradingPeriodConfig

# 验证周期是否支持
period = '30m'
if TradingPeriodConfig.validate_period(period):
    period_info = TradingPeriodConfig.get_period_info(period)
    print(f"支持 {period}: {period_info['name']}")
else:
    print(f"不支持周期: {period}")
```

### 查看所有配置

```python
from trading_period_config import TradingPeriodConfig

# 显示所有支持的周期
TradingPeriodConfig.print_all_periods()

# 显示所有交易模板
from trading_period_config import print_trading_templates
print_trading_templates()
```

## ⚠️ 重要注意事项

### 1. 周期选择建议

- **短周期 (1m, 5m)**:
  - ✅ 反应快速，能捕捉短期机会
  - ❌ 信号噪音多，假信号较多
  - ❌ 交易频率高，手续费成本高
  - 💡 适合经验丰富的短线交易者

- **中周期 (15m, 30m, 1h)**:
  - ✅ 平衡了反应速度和稳定性
  - ✅ 信号质量较好
  - ✅ 交易频率适中
  - 💡 推荐大多数交易者使用

- **长周期 (1d)**:
  - ✅ 信号最稳定，假信号少
  - ✅ 适合长线投资
  - ❌ 反应较慢，可能错过短期机会
  - 💡 适合保守型投资者

### 2. 风险管理

- **短周期交易**: 建议设置更严格的止损
- **长周期交易**: 可以设置相对宽松的止损
- **资金管理**: 短周期建议使用较小的仓位
- **监控频率**: 短周期需要更频繁的监控

### 3. 回测建议

在实盘使用前，强烈建议：

1. 对选定的周期进行充分的历史回测
2. 在模拟环境中运行一段时间
3. 从小仓位开始实盘测试
4. 根据实际表现调整参数

## 📈 性能优化建议

### 1. 数据获取优化

- 短周期: 减少回看周期数，避免获取过多数据
- 长周期: 可以增加回看周期数，获得更稳定的信号

### 2. 更新频率优化

- 可以设置更新间隔略大于周期时间
- 例如：5分钟周期可以设置6分钟更新间隔

### 3. 资源使用优化

- 短周期系统消耗更多CPU和网络资源
- 建议根据服务器性能选择合适的周期

## 🔄 迁移指南

如果您之前使用的是固定1小时周期的版本，迁移到新版本很简单：

### 旧版本代码:
```python
trading_system = LiveTradingSystem(
    symbol="AAPL.US",
    position_size=1,
    update_interval=3600
)
```

### 新版本代码:
```python
trading_system = LiveTradingSystem(
    symbol="AAPL.US",
    position_size=1,
    period='1h',  # 明确指定1小时周期
    update_interval=None  # 自动设置为3600秒
)
```

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看系统日志获取详细错误信息
2. 确认选择的周期在支持列表中
3. 验证API权限和网络连接
4. 检查数据获取是否正常

---

**更新日期**: 2025-08-01  
**版本**: v2.0 - 支持可配置交易周期
