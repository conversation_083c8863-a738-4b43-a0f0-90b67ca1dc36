"""
测试可配置周期的交易系统
======================

验证新的周期配置功能是否正常工作
"""

import sys
import logging

# 设置日志级别，减少输出
logging.getLogger().setLevel(logging.ERROR)

def test_period_config():
    """测试周期配置功能"""
    print("🧪 测试周期配置功能...")
    
    try:
        from trading_period_config import TradingPeriodConfig, get_trading_template
        
        # 测试1: 验证所有周期
        print("✅ 测试1: 验证所有支持的周期")
        all_periods = TradingPeriodConfig.get_all_periods()
        expected_periods = ['1m', '5m', '15m', '30m', '1h', '1d']
        
        for period in expected_periods:
            if period in all_periods:
                print(f"   ✅ {period}: {all_periods[period]['name']}")
            else:
                print(f"   ❌ {period}: 未找到")
        
        # 测试2: 获取推荐配置
        print("\n✅ 测试2: 获取推荐配置")
        test_periods = ['1m', '15m', '1h']
        for period in test_periods:
            config = TradingPeriodConfig.get_recommended_config(period)
            print(f"   {period}: 更新间隔={config['update_interval']}秒, MACD={config['macd_config']}")
        
        # 测试3: 交易模板
        print("\n✅ 测试3: 交易模板")
        templates = ['scalping', 'day_trading', 'swing_trading', 'position_trading']
        for template in templates:
            config = get_trading_template(template)
            print(f"   {template}: {config['name']}, 周期={config['period']}")
        
        print("\n🎉 周期配置功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 周期配置测试失败: {e}")
        return False

def test_strategy_creation():
    """测试策略创建功能"""
    print("\n🧪 测试策略创建功能...")
    
    try:
        # 由于可能没有longport模块，我们只测试配置部分
        from trading_period_config import TradingPeriodConfig
        
        # 测试不同周期的配置
        test_cases = [
            {'period': '5m', 'symbol': 'AAPL.US'},
            {'period': '1h', 'symbol': 'TSLA.US'},
            {'period': '1d', 'symbol': 'SPY.US'},
        ]
        
        for case in test_cases:
            period = case['period']
            symbol = case['symbol']
            
            # 验证周期
            if TradingPeriodConfig.validate_period(period):
                period_info = TradingPeriodConfig.get_period_info(period)
                config = TradingPeriodConfig.get_recommended_config(period)
                
                print(f"   ✅ {symbol} - {period}:")
                print(f"      周期名称: {period_info['name']}")
                print(f"      更新间隔: {config['update_interval']}秒")
                print(f"      回看周期: {config['lookback_periods']}")
            else:
                print(f"   ❌ {symbol} - {period}: 不支持的周期")
        
        print("\n🎉 策略创建功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 策略创建测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        from trading_period_config import TradingPeriodConfig, get_trading_template
        
        # 测试无效周期
        try:
            TradingPeriodConfig.get_period_info('invalid_period')
            print("   ❌ 应该抛出错误但没有")
            return False
        except ValueError:
            print("   ✅ 无效周期正确抛出错误")
        
        # 测试无效模板
        try:
            get_trading_template('invalid_template')
            print("   ❌ 应该抛出错误但没有")
            return False
        except ValueError:
            print("   ✅ 无效模板正确抛出错误")
        
        print("\n🎉 错误处理测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("🎯 MACD交易系统 - 可配置周期功能测试")
    print("="*60)
    
    # 运行所有测试
    tests = [
        test_period_config,
        test_strategy_creation,
        test_error_handling,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    # 输出测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    
    if passed == total:
        print("🎉 所有测试通过！可配置周期功能正常工作。")
        print("\n💡 使用提示:")
        print("1. 支持6种交易周期：1m, 5m, 15m, 30m, 1h, 1d")
        print("2. 提供4种预定义交易模板")
        print("3. 每个周期都有优化的参数配置")
        print("4. 更新间隔可以自动根据周期设置")
    else:
        print("❌ 部分测试失败，请检查配置。")
    
    print("="*60)

if __name__ == "__main__":
    main()
