# from time import sleep
# from longport.openapi import Config, QuoteContext, SubType, PushQuote

# # Load configuration from environment variables
# config = Config.from_env()

# # A callback to receive quote data
# def on_quote(symbol: str, event: PushQuote):
#     print(symbol, event)

# # Create a context for quote APIs
# ctx = QuoteContext(config)
# ctx.set_on_quote(on_quote)

# # Subscribe
# resp = ctx.subscribe(["700.HK"], [SubType.Quote], is_first_push=True)

# # Receive push duration to 30 seconds
# sleep(300)


from longport.openapi import QuoteContext, Config, Period, AdjustType

from longport.openapi import QuoteContext, Config, Period, AdjustType

# 先查看Period类有哪些属性
print("Period类的所有属性：")
print([attr for attr in dir(Period) if not attr.startswith('_')])

config = Config.from_env()
ctx = QuoteContext(config)

# 获取 700.HK 的1分钟 K 线数据
resp = ctx.candlesticks(
    "YINN.US", 
    Period.Day,  # 1分钟周期
    100,  # 获取100根 K 线
    AdjustType.NoAdjust  # 不复权
)
print(resp)