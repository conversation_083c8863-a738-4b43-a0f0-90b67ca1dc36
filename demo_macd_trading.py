"""
MACD交易系统演示脚本
===================

演示如何使用MACD直方图差分交易系统，包括：
1. 配置系统
2. 获取数据
3. 计算指标
4. 生成信号
5. 模拟交易
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 尝试导入matplotlib，如果失败则跳过绘图功能
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib未安装，将跳过绘图功能")

# 导入系统组件
from macd_trading_config import get_full_config, print_config
from enhanced_macd_trading import EnhancedMACDStrategy

def create_demo_data(days=30):
    """
    创建演示用的价格数据
    
    Args:
        days (int): 数据天数
        
    Returns:
        pd.DataFrame: 模拟的1小时K线数据
    """
    print(f"📊 创建 {days} 天的模拟1小时K线数据...")
    
    # 创建时间索引（1小时间隔）
    start_date = datetime.now() - timedelta(days=days)
    dates = pd.date_range(start_date, periods=days*24, freq='H')
    
    # 设置随机种子，确保结果可重现
    np.random.seed(42)
    
    # 生成模拟价格走势
    # 使用随机游走模型，加入一些趋势性
    returns = np.random.randn(len(dates)) * 0.02  # 2%的波动率
    
    # 添加一些趋势性变化
    trend = np.sin(np.arange(len(dates)) * 2 * np.pi / (24 * 7)) * 0.01  # 周期性趋势
    returns += trend
    
    # 计算价格
    initial_price = 150.0
    prices = initial_price * np.exp(np.cumsum(returns))
    
    # 生成OHLCV数据
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    
    # 生成开盘价（基于前一个收盘价加小幅波动）
    data['open'] = data['close'].shift(1) * (1 + np.random.randn(len(dates)) * 0.005)
    data['open'].iloc[0] = initial_price
    
    # 生成最高价和最低价
    high_factor = 1 + np.abs(np.random.randn(len(dates))) * 0.015
    low_factor = 1 - np.abs(np.random.randn(len(dates))) * 0.015
    
    data['high'] = np.maximum(data['open'], data['close']) * high_factor
    data['low'] = np.minimum(data['open'], data['close']) * low_factor
    
    # 生成成交量
    data['volume'] = np.random.randint(100000, 1000000, len(dates))
    
    print(f"✅ 数据创建完成，共 {len(data)} 条记录")
    print(f"   时间范围: {data.index[0]} 到 {data.index[-1]}")
    print(f"   价格范围: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
    
    return data

def demo_macd_calculation(data):
    """演示MACD计算过程"""
    print("\n🔧 演示MACD指标计算...")
    
    # 创建策略实例（仅用于计算，不连接API）
    config = get_full_config()
    strategy = EnhancedMACDStrategy.__new__(EnhancedMACDStrategy)
    strategy.config = config
    strategy.macd_config = config['macd']
    strategy.filter_config = config['filter']
    strategy.signal_config = config['signal']

    # 设置logger
    import logging
    strategy.logger = logging.getLogger('demo_strategy')
    strategy.logger.setLevel(logging.ERROR)
    
    # 计算MACD
    macd_data = strategy.calculate_macd(data)
    
    if not macd_data.empty:
        print("✅ MACD计算成功")
        print(f"   MACD线范围: {macd_data['macd'].min():.6f} 到 {macd_data['macd'].max():.6f}")
        print(f"   信号线范围: {macd_data['signal'].min():.6f} 到 {macd_data['signal'].max():.6f}")
        print(f"   直方图范围: {macd_data['histogram'].min():.6f} 到 {macd_data['histogram'].max():.6f}")
        
        return macd_data
    else:
        print("❌ MACD计算失败")
        return pd.DataFrame()

def demo_signal_generation(data, macd_data):
    """演示信号生成过程"""
    print("\n⚡ 演示交易信号生成...")
    
    # 创建策略实例
    config = get_full_config()
    strategy = EnhancedMACDStrategy.__new__(EnhancedMACDStrategy)
    strategy.config = config
    strategy.macd_config = config['macd']
    strategy.filter_config = config['filter']
    strategy.signal_config = config['signal']

    # 初始化属性
    strategy.histogram_history = []
    strategy.filtered_histogram_history = []
    strategy.diff_history = []

    # 设置logger
    import logging
    strategy.logger = logging.getLogger('demo_strategy')
    strategy.logger.setLevel(logging.ERROR)
    
    # 获取直方图数据
    histogram_values = macd_data['histogram'].dropna().tolist()
    strategy.histogram_history = histogram_values
    
    # 滤波处理
    if len(strategy.histogram_history) >= strategy.filter_config['filter_window']:
        strategy.filtered_histogram_history = strategy.apply_filter(strategy.histogram_history)
        
        print(f"✅ 直方图滤波完成")
        print(f"   原始直方图长度: {len(strategy.histogram_history)}")
        print(f"   滤波后长度: {len(strategy.filtered_histogram_history)}")
        
        # 生成所有信号
        signals = []
        signal_times = []
        
        # 逐步生成信号（模拟实时过程）
        for i in range(3, len(strategy.filtered_histogram_history)):
            # 临时设置历史数据
            temp_history = strategy.filtered_histogram_history[:i+1]
            strategy.filtered_histogram_history = temp_history
            strategy.diff_history = []  # 重置差分历史
            
            signal = strategy.generate_signal()
            if signal != 0:
                signals.append(signal)
                signal_times.append(macd_data.index[i])
                
                action = "买入" if signal == 1 else "卖出"
                print(f"   {macd_data.index[i].strftime('%Y-%m-%d %H:%M')}: {action}信号")
        
        print(f"✅ 信号生成完成，共产生 {len(signals)} 个交易信号")
        return signals, signal_times
    
    else:
        print("❌ 数据不足，无法生成信号")
        return [], []

def demo_trading_simulation(data, signals, signal_times):
    """演示交易模拟"""
    print("\n💰 演示交易模拟...")
    
    if not signals or not signal_times:
        print("❌ 无交易信号，跳过模拟")
        return
    
    # 模拟交易参数
    initial_cash = 100000  # 初始资金
    position_size = 1      # 每次交易1手
    position = 0           # 当前持仓
    cash = initial_cash    # 当前现金
    trades = []            # 交易记录
    
    print(f"📈 开始交易模拟")
    print(f"   初始资金: ${initial_cash:,.2f}")
    print(f"   交易规模: {position_size}手")
    
    for i, (signal, signal_time) in enumerate(zip(signals, signal_times)):
        # 获取当时的价格
        price = data.loc[signal_time, 'close']
        
        if signal == 1 and position == 0:  # 买入信号且无持仓
            position = position_size
            cash -= price * position_size
            trades.append({
                'time': signal_time,
                'action': '买入',
                'price': price,
                'quantity': position_size,
                'cash': cash,
                'position': position
            })
            print(f"   {signal_time.strftime('%m-%d %H:%M')}: 买入 {position_size}手 @ ${price:.2f}")
            
        elif signal == -1 and position > 0:  # 卖出信号且有持仓
            cash += price * position
            trades.append({
                'time': signal_time,
                'action': '卖出',
                'price': price,
                'quantity': position,
                'cash': cash,
                'position': 0
            })
            print(f"   {signal_time.strftime('%m-%d %H:%M')}: 卖出 {position}手 @ ${price:.2f}")
            position = 0
    
    # 计算最终结果
    final_value = cash
    if position > 0:
        final_price = data['close'].iloc[-1]
        final_value += position * final_price
        print(f"   最终持仓: {position}手 @ ${final_price:.2f}")
    
    total_return = (final_value - initial_cash) / initial_cash * 100
    
    print(f"\n📊 交易模拟结果:")
    print(f"   总交易次数: {len(trades)}")
    print(f"   初始资金: ${initial_cash:,.2f}")
    print(f"   最终价值: ${final_value:,.2f}")
    print(f"   总收益率: {total_return:.2f}%")
    
    return trades

def plot_results(data, macd_data, signals, signal_times):
    """绘制结果图表"""
    print("\n📈 绘制结果图表...")

    if not MATPLOTLIB_AVAILABLE:
        print("❌ matplotlib未安装，跳过绘图")
        return

    try:
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
        
        # 绘制价格和交易信号
        ax1.plot(data.index, data['close'], label='收盘价', linewidth=1)
        
        # 标记交易信号
        buy_signals = [time for signal, time in zip(signals, signal_times) if signal == 1]
        sell_signals = [time for signal, time in zip(signals, signal_times) if signal == -1]
        
        if buy_signals:
            buy_prices = [data.loc[time, 'close'] for time in buy_signals]
            ax1.scatter(buy_signals, buy_prices, color='green', marker='^', s=100, label='买入信号')
        
        if sell_signals:
            sell_prices = [data.loc[time, 'close'] for time in sell_signals]
            ax1.scatter(sell_signals, sell_prices, color='red', marker='v', s=100, label='卖出信号')
        
        ax1.set_title('价格走势和交易信号')
        ax1.set_ylabel('价格 ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 绘制MACD
        ax2.plot(macd_data.index, macd_data['macd'], label='MACD线', linewidth=1)
        ax2.plot(macd_data.index, macd_data['signal'], label='信号线', linewidth=1)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.set_title('MACD指标')
        ax2.set_ylabel('MACD值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 绘制MACD直方图
        ax3.bar(macd_data.index, macd_data['histogram'], width=0.02, alpha=0.7, label='MACD直方图')
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax3.set_title('MACD直方图')
        ax3.set_ylabel('直方图值')
        ax3.set_xlabel('时间')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('macd_trading_demo.png', dpi=300, bbox_inches='tight')
        print("✅ 图表已保存为 'macd_trading_demo.png'")
        
        # 显示图表（如果在支持的环境中）
        try:
            plt.show()
        except:
            print("   注意: 无法显示图表，但已保存到文件")
            
    except Exception as e:
        print(f"❌ 绘图失败: {e}")

def main():
    """主演示函数"""
    print("="*70)
    print("🎯 MACD直方图差分交易系统演示")
    print("="*70)
    
    # 显示配置信息
    print("\n📋 系统配置:")
    print_config()
    
    # 1. 创建演示数据
    data = create_demo_data(days=30)
    
    # 2. 计算MACD指标
    macd_data = demo_macd_calculation(data)
    
    if macd_data.empty:
        print("❌ 无法继续演示，MACD计算失败")
        return
    
    # 3. 生成交易信号
    signals, signal_times = demo_signal_generation(data, macd_data)
    
    # 4. 模拟交易
    trades = demo_trading_simulation(data, signals, signal_times)
    
    # 5. 绘制结果
    plot_results(data, macd_data, signals, signal_times)
    
    print("\n" + "="*70)
    print("🎉 演示完成！")
    print("="*70)
    print("📝 演示总结:")
    print("1. ✅ 成功创建了模拟的1小时K线数据")
    print("2. ✅ 计算了MACD指标（快线、慢线、信号线、直方图）")
    print("3. ✅ 对直方图进行了滤波处理")
    print("4. ✅ 基于差分符号变化生成了交易信号")
    print("5. ✅ 模拟了完整的交易过程")
    print("6. ✅ 生成了可视化图表")
    print("\n💡 提示:")
    print("- 这是演示版本，使用模拟数据")
    print("- 实盘使用时需要配置Longbridge API")
    print("- 建议先进行充分的回测验证")
    print("- 实盘交易前请确保理解所有风险")
    print("="*70)

if __name__ == "__main__":
    main()
