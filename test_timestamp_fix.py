"""
测试时间戳处理修复
================

验证修复后的时间戳处理是否正常工作
"""

import pandas as pd

def test_timestamp_conversion():
    """测试时间戳转换函数"""
    print("🧪 测试时间戳转换修复...")
    
    # 测试数据
    test_cases = [
        # 数值型时间戳
        1642896600,  # 2022-01-22 21:30:00 UTC
        1642896600.0,  # 浮点数时间戳
        
        # 字符串格式
        '2025-07-22 21:30:00',
        '2025-07-22T21:30:00',
        '2025-07-22 21:30:00+00:00',
    ]
    
    print("测试不同格式的时间戳转换:")
    
    for i, timestamp in enumerate(test_cases):
        try:
            # 使用修复后的逻辑
            if isinstance(timestamp, (int, float)):
                # 数值型时间戳，使用unit='s'
                datetime_val = pd.to_datetime(timestamp, unit='s')
                print(f"  {i+1}. 数值型 {timestamp} -> {datetime_val}")
            else:
                # 字符串格式，直接转换
                datetime_val = pd.to_datetime(timestamp)
                print(f"  {i+1}. 字符串 '{timestamp}' -> {datetime_val}")
                
        except Exception as e:
            print(f"  {i+1}. ❌ 转换失败: {timestamp} -> {e}")
    
    print("\n✅ 时间戳转换测试完成")

def test_original_error():
    """重现原始错误"""
    print("\n🔍 重现原始错误...")
    
    # 这会导致原始错误
    try:
        result = pd.to_datetime('2025-07-22 21:30:00', unit='s')
        print(f"意外成功: {result}")
    except ValueError as e:
        print(f"✅ 成功重现错误: {e}")
    
    # 修复后的方法
    try:
        timestamp = '2025-07-22 21:30:00'
        if isinstance(timestamp, (int, float)):
            result = pd.to_datetime(timestamp, unit='s')
        else:
            result = pd.to_datetime(timestamp)
        print(f"✅ 修复后成功: {result}")
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    print("="*50)
    print("🔧 时间戳处理修复验证")
    print("="*50)
    
    test_timestamp_conversion()
    test_original_error()
    
    print("\n" + "="*50)
    print("📝 总结:")
    print("- 原始错误: pd.to_datetime(字符串, unit='s') 会失败")
    print("- 修复方案: 根据数据类型选择合适的转换方法")
    print("- 数值型时间戳: 使用 unit='s' 参数")
    print("- 字符串格式: 直接转换，不使用 unit 参数")
    print("="*50)
