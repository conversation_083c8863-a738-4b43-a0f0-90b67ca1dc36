"""
CSV缓存功能测试脚本
==================

测试修改后的CSV格式缓存功能是否正常工作。

运行前请确保：
1. 已设置LongBridge API环境变量
2. 已安装所需的Python包

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import pandas as pd
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from lB_BT_Plotly import BacktestSystem, LongBridgeData, DataCacheManager
    print("✓ 成功导入CSV缓存功能模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)

def test_csv_cache_manager():
    """测试CSV缓存管理器基本功能"""
    print("\n=== 测试CSV缓存管理器 ===")
    
    try:
        # 创建缓存管理器
        cache_manager = DataCacheManager("csv_test_cache")
        print("✓ CSV缓存管理器创建成功")
        
        # 创建模拟数据
        dates = pd.date_range(start='2023-01-01', end='2023-01-10', freq='D')
        mock_data = pd.DataFrame({
            'open': [100.0 + i for i in range(len(dates))],
            'high': [105.0 + i for i in range(len(dates))],
            'low': [95.0 + i for i in range(len(dates))],
            'close': [102.0 + i for i in range(len(dates))],
            'volume': [1000000 + i*10000 for i in range(len(dates))]
        }, index=dates)
        
        print(f"✓ 创建模拟数据: {mock_data.shape}")
        
        # 测试保存数据到CSV
        symbol = "TEST.US"
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 10)
        
        success = cache_manager.save_data(symbol, start_date, end_date, mock_data)
        if success:
            print("✓ 数据保存到CSV成功")
        else:
            print("✗ 数据保存到CSV失败")
            return False
        
        # 检查CSV文件是否存在
        cache_key = cache_manager._generate_cache_key(symbol, start_date, end_date)
        csv_file = cache_manager._get_cache_file_path(cache_key)
        
        if os.path.exists(csv_file):
            print(f"✓ CSV文件已创建: {csv_file}")
            
            # 查看CSV文件内容
            print("\n--- CSV文件内容预览 ---")
            with open(csv_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:6]):  # 显示前6行
                    print(f"第{i+1}行: {line.strip()}")
                if len(lines) > 6:
                    print(f"... (共{len(lines)}行)")
        else:
            print("✗ CSV文件未创建")
            return False
        
        # 测试从CSV读取数据
        loaded_data = cache_manager.load_data(symbol, start_date, end_date)
        if loaded_data is not None:
            print(f"✓ 从CSV读取数据成功: {loaded_data.shape}")
            
            # 验证数据一致性
            print("\n--- 数据一致性检查 ---")
            print(f"原始数据形状: {mock_data.shape}")
            print(f"加载数据形状: {loaded_data.shape}")
            print(f"原始数据列: {list(mock_data.columns)}")
            print(f"加载数据列: {list(loaded_data.columns)}")
            print(f"原始数据索引类型: {type(mock_data.index)}")
            print(f"加载数据索引类型: {type(loaded_data.index)}")
            
            # 检查数值是否一致
            if loaded_data.shape == mock_data.shape:
                print("✓ 数据形状一致")
                
                # 检查数值
                close_match = loaded_data['close'].equals(mock_data['close'])
                print(f"✓ 收盘价数据一致: {close_match}")
                
                if close_match:
                    print("✓ 数据一致性验证通过")
                else:
                    print("✗ 数据一致性验证失败")
                    print("原始收盘价前5个:", mock_data['close'].head().tolist())
                    print("加载收盘价前5个:", loaded_data['close'].head().tolist())
            else:
                print("✗ 数据形状不一致")
                return False
        else:
            print("✗ 从CSV读取数据失败")
            return False
        
        # 测试缓存信息
        info = cache_manager.get_cache_info()
        print(f"✓ 缓存信息: {info}")
        
        return True
        
    except Exception as e:
        print(f"✗ CSV缓存管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_backtest_system():
    """测试CSV缓存的回测系统"""
    print("\n=== 测试CSV缓存回测系统 ===")
    
    try:
        # 创建启用CSV缓存的回测系统
        system = BacktestSystem(enable_cache=True, cache_dir="csv_test_cache")
        print("✓ CSV缓存回测系统创建成功")
        
        # 显示缓存信息
        system.print_cache_info()
        
        return True
        
    except Exception as e:
        print(f"✗ CSV缓存回测系统测试失败: {e}")
        return False

def test_csv_file_readability():
    """测试CSV文件的可读性"""
    print("\n=== 测试CSV文件可读性 ===")
    
    try:
        cache_dir = "csv_test_cache"
        
        # 查找CSV文件
        csv_files = []
        if os.path.exists(cache_dir):
            for file in os.listdir(cache_dir):
                if file.endswith('.csv'):
                    csv_files.append(os.path.join(cache_dir, file))
        
        if not csv_files:
            print("✗ 未找到CSV缓存文件")
            return False
        
        for csv_file in csv_files:
            print(f"\n--- 检查文件: {csv_file} ---")
            
            # 使用pandas读取
            try:
                df = pd.read_csv(csv_file)
                print(f"✓ 文件可读取，形状: {df.shape}")
                print(f"✓ 列名: {list(df.columns)}")
                print(f"✓ 前3行数据:")
                print(df.head(3).to_string())
                
                # 检查是否包含必要的列
                required_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume']
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    print(f"✗ 缺少必要列: {missing_cols}")
                else:
                    print("✓ 包含所有必要列")
                
            except Exception as e:
                print(f"✗ 文件读取失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ CSV文件可读性测试失败: {e}")
        return False

def cleanup_csv_test_cache():
    """清理CSV测试缓存"""
    print("\n=== 清理CSV测试缓存 ===")
    
    try:
        cache_manager = DataCacheManager("csv_test_cache")
        cache_manager.clear_cache()
        print("✓ CSV测试缓存清理完成")
        
        # 删除测试缓存目录
        import shutil
        if os.path.exists("csv_test_cache"):
            shutil.rmtree("csv_test_cache")
            print("✓ CSV测试缓存目录删除完成")
            
    except Exception as e:
        print(f"✗ 清理CSV测试缓存失败: {e}")

def main():
    """主测试函数"""
    print("LongBridge回测系统CSV缓存功能测试")
    print("="*50)
    
    tests = [
        ("CSV缓存管理器基本功能", test_csv_cache_manager),
        ("CSV缓存回测系统", test_csv_backtest_system),
        ("CSV文件可读性", test_csv_file_readability),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    # 清理测试数据
    cleanup_csv_test_cache()
    
    # 显示测试结果
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    if passed == total:
        print("🎉 所有测试通过！CSV缓存功能正常工作。")
        print("📁 缓存文件以CSV格式保存，可以直接用Excel等工具打开查看。")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
