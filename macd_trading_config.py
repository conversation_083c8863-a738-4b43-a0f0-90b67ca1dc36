"""
MACD直方图交易系统配置文件
==========================

用于配置MACD直方图差分交易策略的各种参数
"""

from longport.openapi import Period

# ==================== 基本交易配置 ====================

# 交易标的配置
TRADING_CONFIG = {
    'symbol': 'AAPL.US',        # 交易标的（可修改为其他股票）
    'position_size': 1,         # 每次交易数量（手）
    'update_interval': 3600,    # 数据更新间隔（秒），3600 = 1小时
    'max_position': 10,         # 最大持仓限制（手）
}

# ==================== MACD指标参数 ====================

MACD_CONFIG = {
    'fast_period': 12,          # MACD快线周期
    'slow_period': 26,          # MACD慢线周期
    'signal_period': 9,         # MACD信号线周期
}

# ==================== 滤波器配置 ====================

FILTER_CONFIG = {
    'filter_window': 5,         # 滤波窗口长度
    'filter_type': 'sma',       # 滤波类型：'sma'(简单移动平均), 'ema'(指数移动平均)
    'ema_alpha': 0.3,          # EMA滤波器平滑因子（仅当filter_type='ema'时使用）
}

# ==================== 交易信号配置 ====================

SIGNAL_CONFIG = {
    'min_threshold': 0.001,     # 最小差分阈值，避免噪音交易
    'signal_confirmation': 1,   # 信号确认周期数
    'enable_signal_filter': True,  # 是否启用信号过滤
}

# ==================== 风险控制配置 ====================

RISK_CONFIG = {
    'max_daily_trades': 20,     # 每日最大交易次数
    'stop_loss_pct': 0.05,      # 止损百分比（5%）
    'take_profit_pct': 0.10,    # 止盈百分比（10%）
    'trading_start_time': '09:30',  # 交易开始时间
    'trading_end_time': '16:00',    # 交易结束时间
    'enable_risk_control': True,    # 是否启用风险控制
}

# ==================== 数据获取配置 ====================

DATA_CONFIG = {
    'period': Period.Min_60,    # 数据周期（1小时）
    'lookback_hours': 100,      # 获取历史数据的小时数
    'adjust_type': 'ForwardAdjust',  # 复权类型
    'enable_cache': True,       # 是否启用数据缓存
}

# ==================== 日志配置 ====================

LOGGING_CONFIG = {
    'log_level': 'INFO',        # 日志级别：DEBUG, INFO, WARNING, ERROR
    'log_to_file': True,        # 是否记录到文件
    'log_to_console': True,     # 是否输出到控制台
    'log_file_prefix': 'macd_trading',  # 日志文件前缀
    'enable_trade_log': True,   # 是否记录详细交易日志
}

# ==================== 监控和报警配置 ====================

MONITORING_CONFIG = {
    'enable_status_report': True,   # 是否启用状态报告
    'status_report_interval': 1800, # 状态报告间隔（秒），1800 = 30分钟
    'enable_email_alerts': False,   # 是否启用邮件报警
    'email_recipients': [],         # 邮件接收者列表
    'alert_on_trade': True,         # 交易时是否报警
    'alert_on_error': True,         # 错误时是否报警
}

# ==================== 回测配置 ====================

BACKTEST_CONFIG = {
    'initial_cash': 100000,     # 回测初始资金
    'commission': 0.001,        # 手续费率
    'slippage': 0.001,         # 滑点
    'enable_backtest': True,    # 是否启用回测功能
}

# ==================== 辅助函数 ====================

def get_full_config():
    """
    获取完整配置
    
    Returns:
        dict: 包含所有配置的字典
    """
    return {
        'trading': TRADING_CONFIG,
        'macd': MACD_CONFIG,
        'filter': FILTER_CONFIG,
        'signal': SIGNAL_CONFIG,
        'risk': RISK_CONFIG,
        'data': DATA_CONFIG,
        'logging': LOGGING_CONFIG,
        'monitoring': MONITORING_CONFIG,
        'backtest': BACKTEST_CONFIG,
    }

def validate_config():
    """
    验证配置参数的有效性
    
    Returns:
        tuple: (is_valid, error_messages)
    """
    errors = []
    
    # 验证MACD参数
    if MACD_CONFIG['fast_period'] >= MACD_CONFIG['slow_period']:
        errors.append("MACD快线周期必须小于慢线周期")
    
    if MACD_CONFIG['signal_period'] <= 0:
        errors.append("MACD信号线周期必须大于0")
    
    # 验证交易参数
    if TRADING_CONFIG['position_size'] <= 0:
        errors.append("交易数量必须大于0")
    
    if TRADING_CONFIG['max_position'] < TRADING_CONFIG['position_size']:
        errors.append("最大持仓必须大于等于单次交易数量")
    
    # 验证滤波参数
    if FILTER_CONFIG['filter_window'] < 1:
        errors.append("滤波窗口长度必须至少为1")
    
    # 验证风险参数
    if RISK_CONFIG['stop_loss_pct'] <= 0 or RISK_CONFIG['stop_loss_pct'] >= 1:
        errors.append("止损百分比必须在0-1之间")
    
    if RISK_CONFIG['take_profit_pct'] <= 0:
        errors.append("止盈百分比必须大于0")
    
    # 验证数据参数
    if DATA_CONFIG['lookback_hours'] <= 0:
        errors.append("历史数据获取小时数必须大于0")
    
    return len(errors) == 0, errors

def print_config():
    """打印当前配置"""
    print("="*50)
    print("📋 MACD直方图交易系统配置")
    print("="*50)
    
    print(f"🎯 交易配置:")
    print(f"   标的: {TRADING_CONFIG['symbol']}")
    print(f"   交易量: {TRADING_CONFIG['position_size']}手")
    print(f"   更新间隔: {TRADING_CONFIG['update_interval']}秒")
    print(f"   最大持仓: {TRADING_CONFIG['max_position']}手")
    
    print(f"\n📊 MACD参数:")
    print(f"   快线周期: {MACD_CONFIG['fast_period']}")
    print(f"   慢线周期: {MACD_CONFIG['slow_period']}")
    print(f"   信号线周期: {MACD_CONFIG['signal_period']}")
    
    print(f"\n🔧 滤波配置:")
    print(f"   滤波窗口: {FILTER_CONFIG['filter_window']}")
    print(f"   滤波类型: {FILTER_CONFIG['filter_type']}")
    
    print(f"\n⚡ 信号配置:")
    print(f"   最小阈值: {SIGNAL_CONFIG['min_threshold']}")
    print(f"   信号确认: {SIGNAL_CONFIG['signal_confirmation']}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   止损: {RISK_CONFIG['stop_loss_pct']:.1%}")
    print(f"   止盈: {RISK_CONFIG['take_profit_pct']:.1%}")
    print(f"   每日最大交易: {RISK_CONFIG['max_daily_trades']}次")
    
    print("="*50)

# 使用示例
if __name__ == "__main__":
    # 验证配置
    is_valid, errors = validate_config()
    
    if is_valid:
        print("✅ 配置验证通过")
        print_config()
    else:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   - {error}")
    
    # 获取完整配置
    full_config = get_full_config()
    print(f"\n📦 配置模块数量: {len(full_config)}")
